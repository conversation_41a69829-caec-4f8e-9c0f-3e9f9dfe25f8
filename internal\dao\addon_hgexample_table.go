// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// addonHgexampleTableDao is the data access object for the table hg_addon_hgexample_table.
// You can define custom methods on it to extend its functionality as needed.
type addonHgexampleTableDao struct {
	*internal.AddonHgexampleTableDao
}

var (
	// AddonHgexampleTable is a globally accessible object for table hg_addon_hgexample_table operations.
	AddonHgexampleTable = addonHgexampleTableDao{internal.NewAddonHgexampleTableDao()}
)

// Add your custom methods and functionality below.
