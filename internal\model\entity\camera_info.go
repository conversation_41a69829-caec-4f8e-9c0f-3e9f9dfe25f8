// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// CameraInfo is the golang structure for table camera_info.
type CameraInfo struct {
	Id         int64       `json:"id"         orm:"id"          description:""`
	AreaId     int64       `json:"areaId"     orm:"area_id"     description:"区域id"`
	CameraCode string      `json:"cameraCode" orm:"camera_code" description:"摄像头编码"`
	CameraName string      `json:"cameraName" orm:"camera_name" description:"摄像头名称"`
	CameraType string      `json:"cameraType" orm:"camera_type" description:"摄像头类型:hik,dahua,uniview"`
	DdCode     string      `json:"ddCode"     orm:"dd_code"     description:"调度系统code"`
	HikCode    string      `json:"hikCode"    orm:"hik_code"    description:"海康系统内唯一物理id"`
	Location   string      `json:"location"   orm:"location"    description:"安装位置"`
	Direction  string      `json:"direction"  orm:"direction"   description:"朝向"`
	Status     int         `json:"status"     orm:"status"      description:"状态:1启用,2禁用"`
	Remark     string      `json:"remark"     orm:"remark"      description:"备注"`
	CreatedBy  int64       `json:"createdBy"  orm:"created_by"  description:"创建者"`
	UpdatedBy  int64       `json:"updatedBy"  orm:"updated_by"  description:"更新者"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`
	DeletedAt  *gtime.Time `json:"deletedAt"  orm:"deleted_at"  description:"删除时间"`
}
