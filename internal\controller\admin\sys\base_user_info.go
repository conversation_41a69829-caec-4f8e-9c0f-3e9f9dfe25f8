// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/baseuserinfo"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	BaseUserInfo = cBaseUserInfo{}
)

type cBaseUserInfo struct{}

// List 查看人员列表列表
func (c *cBaseUserInfo) List(ctx context.Context, req *baseuserinfo.ListReq) (res *baseuserinfo.ListRes, err error) {
	list, totalCount, err := service.SysBaseUserInfo().List(ctx, &req.BaseUserInfoListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.BaseUserInfoListModel{}
	}

	res = new(baseuserinfo.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出人员列表列表
func (c *cBaseUserInfo) Export(ctx context.Context, req *baseuserinfo.ExportReq) (res *baseuserinfo.ExportRes, err error) {
	err = service.SysBaseUserInfo().Export(ctx, &req.BaseUserInfoListInp)
	return
}

// Edit 更新人员列表
func (c *cBaseUserInfo) Edit(ctx context.Context, req *baseuserinfo.EditReq) (res *baseuserinfo.EditRes, err error) {
	err = service.SysBaseUserInfo().Edit(ctx, &req.BaseUserInfoEditInp)
	return
}

// View 获取指定人员列表信息
func (c *cBaseUserInfo) View(ctx context.Context, req *baseuserinfo.ViewReq) (res *baseuserinfo.ViewRes, err error) {
	data, err := service.SysBaseUserInfo().View(ctx, &req.BaseUserInfoViewInp)
	if err != nil {
		return
	}

	res = new(baseuserinfo.ViewRes)
	res.BaseUserInfoViewModel = data
	return
}

// Delete 删除人员列表
func (c *cBaseUserInfo) Delete(ctx context.Context, req *baseuserinfo.DeleteReq) (res *baseuserinfo.DeleteRes, err error) {
	err = service.SysBaseUserInfo().Delete(ctx, &req.BaseUserInfoDeleteInp)
	return
}

// Status 更新人员列表状态
func (c *cBaseUserInfo) Status(ctx context.Context, req *baseuserinfo.StatusReq) (res *baseuserinfo.StatusRes, err error) {
	err = service.SysBaseUserInfo().Status(ctx, &req.BaseUserInfoStatusInp)
	return
}
