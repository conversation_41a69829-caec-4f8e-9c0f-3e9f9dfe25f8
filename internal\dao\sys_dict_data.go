// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysDictDataDao is the data access object for the table hg_sys_dict_data.
// You can define custom methods on it to extend its functionality as needed.
type sysDictDataDao struct {
	*internal.SysDictDataDao
}

var (
	// SysDictData is a globally accessible object for table hg_sys_dict_data operations.
	SysDictData = sysDictDataDao{internal.NewSysDictDataDao()}
)

// Add your custom methods and functionality below.
