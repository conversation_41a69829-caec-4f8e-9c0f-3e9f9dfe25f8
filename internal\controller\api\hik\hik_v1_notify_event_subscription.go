package hik

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"intellos/internal/model/entity"
	"intellos/internal/model/enums"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	v1 "intellos/api/api/hik/v1"
)

func (c *ControllerV1) NotifyEventSubscription(ctx context.Context, req *v1.NotifyEventSubscriptionReq) (res *v1.NotifyEventSubscriptionRes, err error) {
	g.Log().Infof(ctx, "收到事件回调消息：%s", gjson.MustEncodeString(req.EventSubscriptionInp))
	events := req.EventSubscriptionInp.Params.Events
	for _, event := range events {
		param := &sysin.HikEventSubscriptionEditInp{
			HikEventSubscription: entity.HikEventSubscription{
				Ability:         req.EventSubscriptionInp.Params.Ability,
				EventId:         event.EventId,
				EventType:       event.EventType,
				SrcIndex:        event.SrcIndex,
				SrcType:         event.SrcType,
				FaceUrl:         event.Data.FaceRecognitionResult.Snap.FaceUrl,
				FaceTime:        event.Data.FaceRecognitionResult.Snap.FaceTime,
				HappenTime:      gtime.New(event.HappenTime),
				SendTime:        gtime.New(req.Params.SendTime),
				SrcName:         event.SrcName,
				ResIndexCode:    event.Data.ResInfo[0].IndexCode,
				ResCn:           event.Data.ResInfo[0].Cn,
				ResResourceType: event.Data.ResInfo[0].ResourceType,
			},
		}
		if len(event.Data.FaceRecognitionResult.FaceMatch) > 0 {
			param.CertificateType = event.Data.FaceRecognitionResult.FaceMatch[0].CertificateType
			param.Certificate = event.Data.FaceRecognitionResult.FaceMatch[0].Certificate
			param.FacePicUrl = event.Data.FaceRecognitionResult.FaceMatch[0].FacePicUrl
			param.FaceInfoName = event.Data.FaceRecognitionResult.FaceMatch[0].FaceInfoName
			param.FaceInfoSex = event.Data.FaceRecognitionResult.FaceMatch[0].FaceInfoSex
		}
		g.Log().Infof(ctx, "保存海康事件回调数据：%+v", param)
		//陌生人时间只取前台
		if event.EventType == int(enums.StrangerEvent) && param.ResIndexCode != "05d33a83431c4193a9f195b7e94af45f" {
			return nil, gerror.New("陌生人事件只允许前台的设备上报")
		}
		err := service.SysHikEventSubscription().Edit(ctx, param)
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}
