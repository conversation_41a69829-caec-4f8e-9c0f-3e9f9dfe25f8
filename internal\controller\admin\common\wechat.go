// Package common

package common

import (
	"context"
	"intellos/api/admin/common"
	"intellos/internal/service"
)

var (
	Wechat = cWechat{}
)

type cWechat struct{}

func (c *cWechat) Authorize(ctx context.Context, req *common.WechatAuthorizeReq) (res *common.WechatAuthorizeRes, err error) {
	_, err = service.CommonWechat().Authorize(ctx, &req.WechatAuthorizeInp)
	return
}

func (c *cWechat) AuthorizeCall(ctx context.Context, req *common.WechatAuthorizeCallReq) (res *common.WechatAuthorizeCallRes, err error) {
	_, err = service.CommonWechat().AuthorizeCall(ctx, &req.WechatAuthorizeCallInp)
	return
}
