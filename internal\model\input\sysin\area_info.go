// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AreaInfoUpdateFields 修改区域管理字段过滤
type AreaInfoUpdateFields struct {
	AreaCode         string `json:"areaCode"         dc:"区域编码"`
	AreaName         string `json:"areaName"         dc:"区域名称"`
	ExternalAreaCode string `json:"externalAreaCode" dc:"外部系统房间编码"`
	Status           int    `json:"status"           dc:"状态:1启用,2禁用"`
	UpdatedBy        int64  `json:"updatedBy"        dc:"更新者"`
	Desc             string `json:"desc"             dc:"描述"`
}

// AreaInfoInsertFields 新增区域管理字段过滤
type AreaInfoInsertFields struct {
	AreaCode         string `json:"areaCode"         dc:"区域编码"`
	AreaName         string `json:"areaName"         dc:"区域名称"`
	ExternalAreaCode string `json:"externalAreaCode" dc:"外部系统房间编码"`
	Status           int    `json:"status"           dc:"状态:1启用,2禁用"`
	CreatedBy        int64  `json:"createdBy"        dc:"创建者"`
	Desc             string `json:"desc"             dc:"描述"`
}

// AreaInfoEditInp 修改/新增区域管理
type AreaInfoEditInp struct {
	entity.AreaInfo
}

func (in *AreaInfoEditInp) Filter(ctx context.Context) (err error) {
	// 验证区域编码
	if err := g.Validator().Rules("required").Data(in.AreaCode).Messages("区域编码不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证区域名称
	if err := g.Validator().Rules("required").Data(in.AreaName).Messages("区域名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type AreaInfoEditModel struct{}

// AreaInfoDeleteInp 删除区域管理
type AreaInfoDeleteInp struct {
	Id interface{} `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *AreaInfoDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type AreaInfoDeleteModel struct{}

// AreaInfoViewInp 获取指定区域管理信息
type AreaInfoViewInp struct {
	Id int64 `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *AreaInfoViewInp) Filter(ctx context.Context) (err error) {
	return
}

type AreaInfoViewModel struct {
	entity.AreaInfo
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// AreaInfoListInp 获取区域管理列表
type AreaInfoListInp struct {
	form.PageReq
	Id       int64  `json:"id"       dc:"id"`
	AreaCode string `json:"areaCode" dc:"区域编码"`
	AreaName string `json:"areaName" dc:"区域名称"`
	Status   int    `json:"status"   dc:"状态:1启用,2禁用"`
	Desc     string `json:"desc"     dc:"描述"`
}

func (in *AreaInfoListInp) Filter(ctx context.Context) (err error) {
	return
}

type AreaInfoListModel struct {
	Id             int64             `json:"id"             dc:"id"`
	AreaCode       string            `json:"areaCode"       dc:"区域编码"`
	AreaName       string            `json:"areaName"       dc:"区域名称"`
	Status         int               `json:"status"         dc:"状态:1启用,2禁用"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBy      int64             `json:"updatedBy"      dc:"更新者"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
	CreatedAt      *gtime.Time       `json:"createdAt"      dc:"created_at"`
	UpdatedAt      *gtime.Time       `json:"updatedAt"      dc:"updated_at"`
	Desc           string            `json:"desc"           dc:"描述"`
}

// AreaInfoStatusInp 更新区域管理状态
type AreaInfoStatusInp struct {
	Id     int64 `json:"id" v:"required#id不能为空" dc:"id"`
	Status int   `json:"status" dc:"状态"`
}

func (in *AreaInfoStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("id不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type AreaInfoStatusModel struct{}