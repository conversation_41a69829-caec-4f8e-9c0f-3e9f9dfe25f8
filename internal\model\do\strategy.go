// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Strategy is the golang structure of table hg_strategy for DAO operations like Where/Data.
type Strategy struct {
	g.Meta       `orm:"table:hg_strategy, do:true"`
	Id           interface{} //
	StrategyCode interface{} // 策略编码
	StrategyName interface{} // 策略名称
	DdRoomCode   interface{} // 调度系统-区域
	Desc         interface{} // 业务描述
	Status       interface{} // 状态:1启用,2禁用
	Remark       interface{} // 备注
	CreatedBy    interface{} // 创建者
	UpdatedBy    interface{} // 更新者
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time // 删除时间
}
