// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// HikEventSubscription is the golang structure for table hik_event_subscription.
type HikEventSubscription struct {
	Id              int64       `json:"id"              orm:"id"                description:"ID"`
	Ability         string      `json:"ability"         orm:"ability"           description:"事件类别"`
	EventId         string      `json:"eventId"         orm:"event_id"          description:"事件唯一标识"`
	EventType       int         `json:"eventType"       orm:"event_type"        description:"事件类型"`
	SrcIndex        string      `json:"srcIndex"        orm:"src_index"         description:"事件源编号"`
	SrcType         string      `json:"srcType"         orm:"src_type"          description:"事件源类型"`
	FaceUrl         string      `json:"faceUrl"         orm:"faceUrl"           description:"人脸图片URI"`
	FaceTime        string      `json:"faceTime"        orm:"faceTime"          description:"抓拍图片的时间"`
	CertificateType string      `json:"certificateType" orm:"certificateType"   description:"目标对应的人脸的证件类型"`
	Certificate     string      `json:"certificate"     orm:"certificate"       description:"目标对应的人脸的证件号码"`
	FacePicUrl      string      `json:"facePicUrl"      orm:"facePicUrl"        description:"目标人脸的图片"`
	HappenTime      *gtime.Time `json:"happenTime"      orm:"happen_time"       description:"事件发生时间"`
	SendTime        *gtime.Time `json:"sendTime"        orm:"send_time"         description:"发送时间"`
	FaceInfoSex     string      `json:"faceInfoSex"     orm:"face_info_sex"     description:"性别"`
	FaceInfoName    string      `json:"faceInfoName"    orm:"face_info_name"    description:"用户信息 (姓名_性别_)"`
	SrcName         string      `json:"srcName"         orm:"src_name"          description:"工作单位"`
	ResIndexCode    string      `json:"resIndexCode"    orm:"res_index_code"    description:"资源的唯一标识"`
	ResCn           string      `json:"resCn"           orm:"res_cn"            description:"资源的名称"`
	ResResourceType string      `json:"resResourceType" orm:"res_resource_type" description:"资源类型"`
}
