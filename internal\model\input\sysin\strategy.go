// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyUpdateFields 修改策略配置字段过滤
type StrategyUpdateFields struct {
	StrategyCode string `json:"strategyCode" dc:"策略ID"`
	StrategyName string `json:"strategyName" dc:"策略名称"`
	DdRoomCode   string `json:"ddRoomCode"   dc:"调度系统区域"`
	Desc         string `json:"desc"         dc:"业务描述"`
	Status       int    `json:"status"       dc:"状态:1启用,2禁用"`
	UpdatedBy    int64  `json:"updatedBy"    dc:"更新者"`
}

// StrategyInsertFields 新增策略配置字段过滤
type StrategyInsertFields struct {
	StrategyCode string `json:"strategyCode" dc:"策略ID"`
	StrategyName string `json:"strategyName" dc:"策略名称"`
	DdRoomCode   string `json:"ddRoomCode"   dc:"调度系统区域"`
	Desc         string `json:"desc"         dc:"业务描述"`
	Status       int    `json:"status"       dc:"状态:1启用,2禁用"`
	CreatedBy    int64  `json:"createdBy"    dc:"创建者"`
}

// StrategyEditInp 修改/新增策略配置
type StrategyEditInp struct {
	entity.Strategy
}

func (in *StrategyEditInp) Filter(ctx context.Context) (err error) {
	// 验证策略ID
	if err := g.Validator().Rules("required").Data(in.StrategyCode).Messages("策略ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	//if err := g.Validator().Rules("in:CL001,CL002,CL003,CL004").Data(in.StrategyCode).Messages("策略ID值不正确").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证策略名称
	if err := g.Validator().Rules("required").Data(in.StrategyName).Messages("策略名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type StrategyEditModel struct{}

// StrategyRelationEditInp 修改/新增策略关联表
type StrategyRelationEditInp struct {
	StrategyArea      []entity.StrategyAreaRelation `json:"strategyArea"`
	StrategyCondition []entity.StrategyCondition    `json:"strategyCondition"`
}

func (in *StrategyRelationEditInp) Filter(ctx context.Context) (err error) {
	return
}

type DDProjectInfoInp struct {
	RoomId         string `json:"roomId" dc:"房间名称"`
	RoomName       string `json:"roomName" dc:"房间名称"`
	BidCouncilId   string `json:"bidCouncilId" dc:"评委会id"`
	BidCouncilName string `json:"bidCouncilName" dc:"评委会名称"`
	ProjectStatus  string `json:"projectStatus" dc:"项目状态"`
}

func (in *DDProjectInfoInp) Filter(ctx context.Context) (err error) {
	return
}

// StrategyDeleteInp 删除策略配置
type StrategyDeleteInp struct {
	Id interface{} `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyDeleteModel struct{}

// StrategyViewInp 获取指定策略配置信息
type StrategyViewInp struct {
	Id int64 `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyViewInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyViewModel struct {
	entity.Strategy
}

// StrategyListInp 获取策略配置列表
type StrategyListInp struct {
	form.PageReq
	Id           int64         `json:"id"           dc:"id"`
	StrategyCode string        `json:"strategyCode" dc:"策略ID"`
	StrategyName string        `json:"strategyName" dc:"策略名称"`
	Status       int           `json:"status"       dc:"状态:1启用,2禁用"`
	CreatedAt    []*gtime.Time `json:"createdAt"    dc:"created_at"`
}

func (in *StrategyListInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyListModel struct {
	Id           int64       `json:"id"           dc:"id"`
	StrategyCode string      `json:"strategyCode" dc:"策略ID"`
	StrategyName string      `json:"strategyName" dc:"策略名称"`
	DdRoomCode   string      `json:"ddRoomCode"   dc:"调度系统区域"`
	Desc         string      `json:"desc"         dc:"业务描述"`
	Status       int         `json:"status"       dc:"状态:1启用,2禁用"`
	CreatedAt    *gtime.Time `json:"createdAt"    dc:"created_at"`
}

// StrategyStatusInp 更新策略配置状态
type StrategyStatusInp struct {
	Id     int64 `json:"id" v:"required#id不能为空" dc:"id"`
	Status int   `json:"status" dc:"状态"`
}

func (in *StrategyStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("id不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type StrategyStatusModel struct{}
