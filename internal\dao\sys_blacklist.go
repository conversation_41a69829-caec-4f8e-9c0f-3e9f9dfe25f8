// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysBlacklistDao is the data access object for the table hg_sys_blacklist.
// You can define custom methods on it to extend its functionality as needed.
type sysBlacklistDao struct {
	*internal.SysBlacklistDao
}

var (
	// SysBlacklist is a globally accessible object for table hg_sys_blacklist operations.
	SysBlacklist = sysBlacklistDao{internal.NewSysBlacklistDao()}
)

// Add your custom methods and functionality below.
