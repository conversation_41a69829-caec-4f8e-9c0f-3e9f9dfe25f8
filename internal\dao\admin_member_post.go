// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminMemberPostDao is the data access object for the table hg_admin_member_post.
// You can define custom methods on it to extend its functionality as needed.
type adminMemberPostDao struct {
	*internal.AdminMemberPostDao
}

var (
	// AdminMemberPost is a globally accessible object for table hg_admin_member_post operations.
	AdminMemberPost = adminMemberPostDao{internal.NewAdminMemberPostDao()}
)

// Add your custom methods and functionality below.
