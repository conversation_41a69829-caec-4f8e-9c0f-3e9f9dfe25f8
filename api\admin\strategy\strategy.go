// Package strategy
// @AutoGenerate Version 2.16.10
package strategy

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询策略配置列表
type ListReq struct {
	g.Meta `path:"/strategy/list" method:"get" tags:"策略配置" summary:"获取策略配置列表"`
	sysin.StrategyListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.StrategyListModel `json:"list"   dc:"数据列表"`
}

// ViewReq 获取策略配置指定信息
type ViewReq struct {
	g.Meta `path:"/strategy/view" method:"get" tags:"策略配置" summary:"获取策略配置指定信息"`
	sysin.StrategyViewInp
}

type ViewRes struct {
	*sysin.StrategyViewModel
}

// EditReq 修改/新增策略配置
type EditReq struct {
	g.Meta `path:"/strategy/edit" method:"post" tags:"策略配置" summary:"修改/新增策略配置"`
	sysin.StrategyEditInp
}

type EditRes struct{}

type RelationEditReq struct {
	g.Meta `path:"/strategyRelation/edit" method:"post" tags:"策略配置" summary:"修改/新增策略关联配置"`
	sysin.StrategyRelationEditInp
}

type RelationEditRes struct{}

// DeleteReq 删除策略配置
type DeleteReq struct {
	g.Meta `path:"/strategy/delete" method:"post" tags:"策略配置" summary:"删除策略配置"`
	sysin.StrategyDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新策略配置状态
type StatusReq struct {
	g.Meta `path:"/strategy/status" method:"post" tags:"策略配置" summary:"更新策略配置状态"`
	sysin.StrategyStatusInp
}

type StatusRes struct{}
