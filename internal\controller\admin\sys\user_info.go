// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/userinfo"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	UserInfo = cUserInfo{}
)

type cUserInfo struct{}

// List 查看人员列表列表
func (c *cUserInfo) List(ctx context.Context, req *userinfo.ListReq) (res *userinfo.ListRes, err error) {
	list, totalCount, err := service.SysUserInfo().List(ctx, &req.UserInfoListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.UserInfoListModel{}
	}

	res = new(userinfo.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出人员列表列表
func (c *cUserInfo) Export(ctx context.Context, req *userinfo.ExportReq) (res *userinfo.ExportRes, err error) {
	err = service.SysUserInfo().Export(ctx, &req.UserInfoListInp)
	return
}

// Edit 更新人员列表
func (c *cUserInfo) Edit(ctx context.Context, req *userinfo.EditReq) (res *userinfo.EditRes, err error) {
	err = service.SysUserInfo().Edit(ctx, &req.UserInfoEditInp)
	return
}

// View 获取指定人员列表信息
func (c *cUserInfo) View(ctx context.Context, req *userinfo.ViewReq) (res *userinfo.ViewRes, err error) {
	data, err := service.SysUserInfo().View(ctx, &req.UserInfoViewInp)
	if err != nil {
		return
	}

	res = new(userinfo.ViewRes)
	res.UserInfoViewModel = data
	return
}

// Delete 删除人员列表
func (c *cUserInfo) Delete(ctx context.Context, req *userinfo.DeleteReq) (res *userinfo.DeleteRes, err error) {
	err = service.SysUserInfo().Delete(ctx, &req.UserInfoDeleteInp)
	return
}

// Status 更新人员列表状态
func (c *cUserInfo) Status(ctx context.Context, req *userinfo.StatusReq) (res *userinfo.StatusRes, err error) {
	err = service.SysUserInfo().Status(ctx, &req.UserInfoStatusInp)
	return
}
