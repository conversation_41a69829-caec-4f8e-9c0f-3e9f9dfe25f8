// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysStrategyAreaRelation struct{}

func NewSysStrategyAreaRelation() *sSysStrategyAreaRelation {
	return &sSysStrategyAreaRelation{}
}

func init() {
	service.RegisterSysStrategyAreaRelation(NewSysStrategyAreaRelation())
}

// Model 区域策略关联表ORM模型
func (s *sSysStrategyAreaRelation) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.StrategyAreaRelation.Ctx(ctx), option...)
}

// List 获取区域策略关联表列表
func (s *sSysStrategyAreaRelation) List(ctx context.Context, in *sysin.StrategyAreaRelationListInp) (list []*sysin.StrategyAreaRelationListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.StrategyAreaRelationListModel{})

	// 查询id
	if in.Id > 0 {
		mod = mod.Where(dao.StrategyAreaRelation.Columns().Id, in.Id)
	}

	// 查询created_at
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.StrategyAreaRelation.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.StrategyAreaRelation.Columns().Id)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取区域策略关联表列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出区域策略关联表
func (s *sSysStrategyAreaRelation) Export(ctx context.Context, in *sysin.StrategyAreaRelationListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.StrategyAreaRelationExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出区域策略关联表-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.StrategyAreaRelationExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增区域策略关联表
func (s *sSysStrategyAreaRelation) Edit(ctx context.Context, in *sysin.StrategyAreaRelationEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			if _, err = s.Model(ctx).
				Fields(sysin.StrategyAreaRelationUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改区域策略关联表失败，请稍后重试！")
			}
			return
		}

		// 新增
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.StrategyAreaRelationInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增区域策略关联表失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除区域策略关联表
func (s *sSysStrategyAreaRelation) Delete(ctx context.Context, in *sysin.StrategyAreaRelationDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除区域策略关联表失败，请稍后重试！")
		return
	}
	return
}

// View 获取区域策略关联表指定信息
func (s *sSysStrategyAreaRelation) View(ctx context.Context, in *sysin.StrategyAreaRelationViewInp) (res *sysin.StrategyAreaRelationViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取区域策略关联表信息，请稍后重试！")
		return
	}
	return
}