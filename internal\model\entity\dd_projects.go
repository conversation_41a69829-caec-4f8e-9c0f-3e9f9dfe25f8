// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DdProjects is the golang structure for table dd_projects.
type DdProjects struct {
	Id             int64  `json:"id"             orm:"id"               description:"ID"`
	RoomId         string `json:"roomId"         orm:"room_id"          description:"房间id"`
	RoomName       string `json:"roomName"       orm:"room_name"        description:"房间名称"`
	BidCouncilId   string `json:"bidCouncilId"   orm:"bid_council_id"   description:"评委会id"`
	BidCouncilName string `json:"bidCouncilName" orm:"bid_council_name" description:"评委会名称"`
	ProjectStatus  string `json:"projectStatus"  orm:"project_status"   description:"项目状态"`
	CreatedBy      int64       `json:"createdBy"      orm:"created_by"       description:"创建者"`
	UpdatedBy      int64       `json:"updatedBy"      orm:"updated_by"       description:"更新者"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       description:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       description:"修改时间"`
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"       description:"删除时间"`
	// 扩展字段：主动获取的数据
	ExtraData      string      `json:"extraData"      orm:"extra_data"       description:"主动获取的额外数据"`
	SyncStatus     int         `json:"syncStatus"     orm:"sync_status"      description:"同步状态:0待同步,1同步中,2同步成功,3同步失败"`
	SyncRetryCount int         `json:"syncRetryCount" orm:"sync_retry_count" description:"同步重试次数"`
	SyncErrorMsg   string      `json:"syncErrorMsg"   orm:"sync_error_msg"   description:"同步错误信息"`
	LastSyncAt     *gtime.Time `json:"lastSyncAt"     orm:"last_sync_at"     description:"最后同步时间"`
}
