// Package projectperson
// @AutoGenerate Version 2.16.10
package projectperson

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询调度系统项目人员列表列表
type ListReq struct {
	g.Meta `path:"/projectPerson/list" method:"get" tags:"调度系统项目人员列表" summary:"获取调度系统项目人员列表列表"`
	sysin.ProjectPersonListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.ProjectPersonListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出调度系统项目人员列表列表
type ExportReq struct {
	g.Meta `path:"/projectPerson/export" method:"get" tags:"调度系统项目人员列表" summary:"导出调度系统项目人员列表列表"`
	sysin.ProjectPersonListInp
}

type ExportRes struct{}

// ViewReq 获取调度系统项目人员列表指定信息
type ViewReq struct {
	g.<PERSON>a `path:"/projectPerson/view" method:"get" tags:"调度系统项目人员列表" summary:"获取调度系统项目人员列表指定信息"`
	sysin.ProjectPersonViewInp
}

type ViewRes struct {
	*sysin.ProjectPersonViewModel
}

// EditReq 修改/新增调度系统项目人员列表
type EditReq struct {
	g.Meta `path:"/projectPerson/edit" method:"post" tags:"调度系统项目人员列表" summary:"修改/新增调度系统项目人员列表"`
	sysin.ProjectPersonEditInp
}

type EditRes struct{}

// DeleteReq 删除调度系统项目人员列表
type DeleteReq struct {
	g.Meta `path:"/projectPerson/delete" method:"post" tags:"调度系统项目人员列表" summary:"删除调度系统项目人员列表"`
	sysin.ProjectPersonDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新调度系统项目人员列表状态
type StatusReq struct {
	g.Meta `path:"/projectPerson/status" method:"post" tags:"调度系统项目人员列表" summary:"更新调度系统项目人员列表状态"`
	sysin.ProjectPersonStatusInp
}

type StatusRes struct{}