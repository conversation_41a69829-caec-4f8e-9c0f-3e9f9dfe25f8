// Package tcpserver

package tcpserver

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"intellos/api/servmsg"
	"intellos/internal/consts"
	"intellos/internal/dao"
	"intellos/internal/library/network/tcp"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/servmsgin"
	"intellos/internal/service"
)

// OnAuthSummary 获取授权信息
func (s *sTCPServer) OnAuthSummary(ctx context.Context, req *servmsg.AuthSummaryReq) {
	var (
		conn   = tcp.ConnFromCtx(ctx)
		models *entity.SysServeLicense
		res    = new(servmsg.AuthSummaryRes)
	)

	if conn == nil {
		g.Log().Warningf(ctx, "conn is nil.")
		return
	}

	if conn.Auth == nil {
		res.SetError(gerror.New("登录信息获取失败，请重新登录"))
		_ = conn.Send(ctx, res)
		return
	}

	if err := dao.SysServeLicense.Ctx(ctx).Where("appid = ?", conn.Auth.AppId).Scan(&models); err != nil {
		res.SetError(err)
		_ = conn.Send(ctx, res)
		return
	}

	if models == nil {
		res.SetError(gerror.New("授权信息不存在"))
		_ = conn.Send(ctx, res)
		return
	}

	if models.Status != consts.StatusEnabled {
		res.SetError(gerror.New("授权已禁用，请联系管理员"))
		_ = conn.Send(ctx, res)
		return
	}

	if models.Group != conn.Auth.Group {
		res.SetError(gerror.New("你登录的授权分组未得到授权，请联系管理员"))
		_ = conn.Send(ctx, res)
		return
	}

	if models.EndAt.Before(gtime.Now()) {
		res.SetError(gerror.New("授权已过期，请联系管理员"))
		_ = conn.Send(ctx, res)
		return
	}

	data := new(servmsgin.AuthSummaryModel)
	data.EndAt = models.EndAt
	data.Online = service.TCPServer().Instance().GetAppIdOnline(models.Appid)

	// 请填充你的授权数据
	// ...

	res.Data = data
	_ = conn.Send(ctx, res)
}
