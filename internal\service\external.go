// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/database/gdb"
)

type (
	ISysHikEventSubscription interface {
		// Model 海康事件订阅表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取海康事件订阅表列表
		List(ctx context.Context, in *sysin.HikEventSubscriptionListInp) (list []*sysin.HikEventSubscriptionListModel, totalCount int, err error)
		// Export 导出海康事件订阅表
		Export(ctx context.Context, in *sysin.HikEventSubscriptionListInp) (err error)
		// Edit 修改/新增海康事件订阅表
		Edit(ctx context.Context, in *sysin.HikEventSubscriptionEditInp) (err error)
		// Delete 删除海康事件订阅表
		Delete(ctx context.Context, in *sysin.HikEventSubscriptionDeleteInp) (err error)
		// View 获取海康事件订阅表指定信息
		View(ctx context.Context, in *sysin.HikEventSubscriptionViewInp) (res *sysin.HikEventSubscriptionViewModel, err error)
	}
)

var (
	localSysHikEventSubscription ISysHikEventSubscription
)

func SysHikEventSubscription() ISysHikEventSubscription {
	if localSysHikEventSubscription == nil {
		panic("implement not found for interface ISysHikEventSubscription, forgot register?")
	}
	return localSysHikEventSubscription
}

func RegisterSysHikEventSubscription(i ISysHikEventSubscription) {
	localSysHikEventSubscription = i
}
