// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysServeLogDao is the data access object for the table hg_sys_serve_log.
// You can define custom methods on it to extend its functionality as needed.
type sysServeLogDao struct {
	*internal.SysServeLogDao
}

var (
	// SysServeLog is a globally accessible object for table hg_sys_serve_log operations.
	SysServeLog = sysServeLogDao{internal.NewSysServeLogDao()}
)

// Add your custom methods and functionality below.
