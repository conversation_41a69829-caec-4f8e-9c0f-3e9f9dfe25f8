// Package userinfo
// @AutoGenerate Version 2.16.10
package userinfo

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询人员列表列表
type ListReq struct {
	g.Meta `path:"/userInfo/list" method:"get" tags:"人员列表" summary:"获取人员列表列表"`
	sysin.UserInfoListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.UserInfoListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出人员列表列表
type ExportReq struct {
	g.Meta `path:"/userInfo/export" method:"get" tags:"人员列表" summary:"导出人员列表列表"`
	sysin.UserInfoListInp
}

type ExportRes struct{}

// ViewReq 获取人员列表指定信息
type ViewReq struct {
	g.Meta `path:"/userInfo/view" method:"get" tags:"人员列表" summary:"获取人员列表指定信息"`
	sysin.UserInfoViewInp
}

type ViewRes struct {
	*sysin.UserInfoViewModel
}

// EditReq 修改/新增人员列表
type EditReq struct {
	g.Meta `path:"/userInfo/edit" method:"post" tags:"人员列表" summary:"修改/新增人员列表"`
	sysin.UserInfoEditInp
}

type EditRes struct{}

// DeleteReq 删除人员列表
type DeleteReq struct {
	g.Meta `path:"/userInfo/delete" method:"post" tags:"人员列表" summary:"删除人员列表"`
	sysin.UserInfoDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新人员列表状态
type StatusReq struct {
	g.Meta `path:"/userInfo/status" method:"post" tags:"人员列表" summary:"更新人员列表状态"`
	sysin.UserInfoStatusInp
}

type StatusRes struct{}
