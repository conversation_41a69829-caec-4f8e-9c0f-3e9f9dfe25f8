package excel

import (
	"context"
	"intellos/internal/model/input/sysin"
	"os"
	"reflect"
	"testing"
)

func TestImportByStructs(t *testing.T) {
	// 打开本地文件
	file, err := os.Open("/Users/<USER>/Downloads/导出登录日志-008d3f23381352180f8972009785c52d.xlsx")
	if err != nil {
		println("打开文件失败", err.Error())
	}
	defer file.Close()

	type args struct {
		ctx context.Context
		f   *os.File
		t   any
	}
	type testCase[T any] struct {
		name       string
		args       args
		wantResult []T
		wantErr    bool
	}
	tests := []testCase[sysin.LoginLogExportModel]{
		{
			"测试导入[登录日志]",
			args{
				ctx: context.TODO(),
				f:   file,
				t:   sysin.LoginLogExportModel{},
			},
			[]sysin.LoginLogExportModel{},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var gotResult []sysin.LoginLogExportModel
			gotResult, err := ImportByStructs[sysin.LoginLogExportModel](tt.args.ctx, tt.args.f)
			if (err != nil) != tt.wantErr {
				t.Errorf("ImportByStructs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResult, tt.wantResult) {
				t.Errorf("ImportByStructs() gotResult = %v, want %v", gotResult, tt.wantResult)
			}
		})
	}
}
