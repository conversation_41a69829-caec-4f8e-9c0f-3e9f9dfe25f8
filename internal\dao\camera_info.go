// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// cameraInfoDao is the data access object for the table hg_camera_info.
// You can define custom methods on it to extend its functionality as needed.
type cameraInfoDao struct {
	*internal.CameraInfoDao
}

var (
	// CameraInfo is a globally accessible object for table hg_camera_info operations.
	CameraInfo = cameraInfoDao{internal.NewCameraInfoDao()}
)

// Add your custom methods and functionality below.
