// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminRoleCasbinDao is the data access object for the table hg_admin_role_casbin.
// You can define custom methods on it to extend its functionality as needed.
type adminRoleCasbinDao struct {
	*internal.AdminRoleCasbinDao
}

var (
	// AdminRoleCasbin is a globally accessible object for table hg_admin_role_casbin operations.
	AdminRoleCasbin = adminRoleCasbinDao{internal.NewAdminRoleCasbinDao()}
)

// Add your custom methods and functionality below.
