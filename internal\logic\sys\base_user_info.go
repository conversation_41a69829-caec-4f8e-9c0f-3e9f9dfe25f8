// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysBaseUserInfo struct{}

func NewSysBaseUserInfo() *sSysBaseUserInfo {
	return &sSysBaseUserInfo{}
}

func init() {
	service.RegisterSysBaseUserInfo(NewSysBaseUserInfo())
}

// Model 人员列表ORM模型
func (s *sSysBaseUserInfo) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.BaseUserInfo.Ctx(ctx), option...)
}

// List 获取人员列表列表
func (s *sSysBaseUserInfo) List(ctx context.Context, in *sysin.BaseUserInfoListInp) (list []*sysin.BaseUserInfoListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.BaseUserInfoListModel{})

	// 查询姓名
	if in.Name != "" {
		mod = mod.WhereLike(dao.BaseUserInfo.Columns().Name, in.Name)
	}

	// 查询身份证
	if in.IdCard != "" {
		mod = mod.WhereLike(dao.BaseUserInfo.Columns().IdCard, in.IdCard)
	}

	// 查询状态
	if in.Status != "" {
		mod = mod.WhereLike(dao.BaseUserInfo.Columns().Status, in.Status)
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.BaseUserInfo.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取人员列表列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出人员列表
func (s *sSysBaseUserInfo) Export(ctx context.Context, in *sysin.BaseUserInfoListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.BaseUserInfoExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出人员列表-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.BaseUserInfoExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增人员列表
func (s *sSysBaseUserInfo) Edit(ctx context.Context, in *sysin.BaseUserInfoEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.BaseUserInfoUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改人员列表失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = 1
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.BaseUserInfoInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增人员列表失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除人员列表
func (s *sSysBaseUserInfo) Delete(ctx context.Context, in *sysin.BaseUserInfoDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除人员列表失败，请稍后重试！")
		return
	}
	return
}

// View 获取人员列表指定信息
func (s *sSysBaseUserInfo) View(ctx context.Context, in *sysin.BaseUserInfoViewInp) (res *sysin.BaseUserInfoViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取人员列表信息，请稍后重试！")
		return
	}
	return
}

// Status 更新人员列表状态
func (s *sSysBaseUserInfo) Status(ctx context.Context, in *sysin.BaseUserInfoStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.BaseUserInfo.Columns().Status:    in.Status,
		dao.BaseUserInfo.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新人员列表状态失败，请稍后重试！")
		return
	}
	return
}
