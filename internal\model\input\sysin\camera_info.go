// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// CameraInfoUpdateFields 修改摄像头管理字段过滤
type CameraInfoUpdateFields struct {
	AreaId     int64  `json:"areaId"     dc:"区域id"`
	CameraCode string `json:"cameraCode" dc:"摄像头编码"`
	CameraName string `json:"cameraName" dc:"摄像头名称"`
	HikCode    string `json:"hikCode"    dc:"海康系统内唯一物理id"`
	Location   string `json:"location"   dc:"安装位置"`
	Status     int    `json:"status"     dc:"状态:1启用,2禁用"`
	UpdatedBy  int64  `json:"updatedBy"  dc:"更新者"`
}

// CameraInfoInsertFields 新增摄像头管理字段过滤
type CameraInfoInsertFields struct {
	AreaId     int64  `json:"areaId"     dc:"区域id"`
	CameraCode string `json:"cameraCode" dc:"摄像头编码"`
	CameraName string `json:"cameraName" dc:"摄像头名称"`
	HikCode    string `json:"hikCode"    dc:"海康系统内唯一物理id"`
	Location   string `json:"location"   dc:"安装位置"`
	Status     int    `json:"status"     dc:"状态:1启用,2禁用"`
	CreatedBy  int64  `json:"createdBy"  dc:"创建者"`
}

// CameraInfoEditInp 修改/新增摄像头管理
type CameraInfoEditInp struct {
	entity.CameraInfo
}

func (in *CameraInfoEditInp) Filter(ctx context.Context) (err error) {
	// 验证摄像头编码
	if err := g.Validator().Rules("required").Data(in.CameraCode).Messages("摄像头编码不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证摄像头名称
	if err := g.Validator().Rules("required").Data(in.CameraName).Messages("摄像头名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type CameraInfoEditModel struct{}

// CameraInfoDeleteInp 删除摄像头管理
type CameraInfoDeleteInp struct {
	Id interface{} `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *CameraInfoDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type CameraInfoDeleteModel struct{}

// CameraInfoViewInp 获取指定摄像头管理信息
type CameraInfoViewInp struct {
	Id int64 `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *CameraInfoViewInp) Filter(ctx context.Context) (err error) {
	return
}

type CameraInfoViewModel struct {
	entity.CameraInfo
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
}

// CameraInfoListInp 获取摄像头管理列表
type CameraInfoListInp struct {
	form.PageReq
	CameraCode string `json:"cameraCode" dc:"摄像头编码"`
	CameraName string `json:"cameraName" dc:"摄像头名称"`
	Status     int    `json:"status"     dc:"状态:1启用,2禁用"`
}

func (in *CameraInfoListInp) Filter(ctx context.Context) (err error) {
	return
}

type CameraInfoListModel struct {
	Id             int64             `json:"id"             dc:"id"`
	AreaId         int64             `json:"areaId"         dc:"区域id"`
	CameraCode     string            `json:"cameraCode"     dc:"摄像头编码"`
	CameraName     string            `json:"cameraName"     dc:"摄像头名称"`
	HikCode        string            `json:"hikCode"        dc:"海康系统内唯一物理id"`
	Location       string            `json:"location"       dc:"安装位置"`
	Status         int               `json:"status"         dc:"状态:1启用,2禁用"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
}

// CameraInfoStatusInp 更新摄像头管理状态
type CameraInfoStatusInp struct {
	Id     int64 `json:"id" v:"required#id不能为空" dc:"id"`
	Status int   `json:"status" dc:"状态"`
}

func (in *CameraInfoStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("id不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type CameraInfoStatusModel struct{}