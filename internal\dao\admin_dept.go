// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminDeptDao is the data access object for the table hg_admin_dept.
// You can define custom methods on it to extend its functionality as needed.
type adminDeptDao struct {
	*internal.AdminDeptDao
}

var (
	// AdminDept is a globally accessible object for table hg_admin_dept operations.
	AdminDept = adminDeptDao{internal.NewAdminDeptDao()}
)

// Add your custom methods and functionality below.
