// Package baseuserinfo
// @AutoGenerate Version 2.16.10
package baseuserinfo

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询人员列表列表
type ListReq struct {
	g.Meta `path:"/baseUserInfo/list" method:"get" tags:"人员列表" summary:"获取人员列表列表"`
	sysin.BaseUserInfoListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.BaseUserInfoListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出人员列表列表
type ExportReq struct {
	g.Meta `path:"/baseUserInfo/export" method:"get" tags:"人员列表" summary:"导出人员列表列表"`
	sysin.BaseUserInfoListInp
}

type ExportRes struct{}

// ViewReq 获取人员列表指定信息
type ViewReq struct {
	g.Meta `path:"/baseUserInfo/view" method:"get" tags:"人员列表" summary:"获取人员列表指定信息"`
	sysin.BaseUserInfoViewInp
}

type ViewRes struct {
	*sysin.BaseUserInfoViewModel
}

// EditReq 修改/新增人员列表
type EditReq struct {
	g.Meta `path:"/baseUserInfo/edit" method:"post" tags:"人员列表" summary:"修改/新增人员列表"`
	sysin.BaseUserInfoEditInp
}

type EditRes struct{}

// DeleteReq 删除人员列表
type DeleteReq struct {
	g.Meta `path:"/baseUserInfo/delete" method:"post" tags:"人员列表" summary:"删除人员列表"`
	sysin.BaseUserInfoDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新人员列表状态
type StatusReq struct {
	g.Meta `path:"/baseUserInfo/status" method:"post" tags:"人员列表" summary:"更新人员列表状态"`
	sysin.BaseUserInfoStatusInp
}

type StatusRes struct{}