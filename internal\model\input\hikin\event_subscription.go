package hikin

import "time"

type EventSubscriptionInp struct {
	Method string `json:"method"`
	Params struct {
		SendTime time.Time `json:"sendTime"`
		Ability  string    `json:"ability"`
		Events   []struct {
			EventId    string    `json:"eventId"`
			EventType  int       `json:"eventType"`
			HappenTime time.Time `json:"happenTime"`
			SrcIndex   string    `json:"srcIndex"`
			SrcName    string    `json:"srcName"`
			SrcType    string    `json:"srcType"`
			Status     int       `json:"status"`
			Timeout    int       `json:"timeout"`
			Data       struct {
				FaceRecognitionResult struct {
					Snap struct {
						AgeGroup string `json:"ageGroup"`
						BkgUrl   string `json:"bkgUrl"`
						FaceTime string `json:"faceTime"`
						FaceUrl  string `json:"faceUrl"`
						Gender   string `json:"gender"`
						Glass    string `json:"glass"`
					} `json:"snap"`
					SrcEventId string `json:"srcEventId"`
					FaceMatch  []struct {
						FaceGroupCode   string  `json:"faceGroupCode"`
						FaceGroupName   string  `json:"faceGroupName"`
						FaceInfoCode    string  `json:"faceInfoCode"`
						FaceInfoName    string  `json:"faceInfoName"`
						FaceInfoSex     string  `json:"faceInfoSex"`
						Certificate     string  `json:"certificate" dc:"目标对应的人脸的证件号码	"`
						CertificateType string  `json:"certificateType" dc:"目标对应的人脸的证件类型"`
						Similarity      float64 `json:"similarity"`
						FacePicUrl      string  `json:"facePicUrl" dc:"目标人脸的图片"`
					} `json:"faceMatch"`
				} `json:"faceRecognitionResult"`

				ResInfo []struct {
					Cn           string `json:"cn"`
					IndexCode    string `json:"indexCode"`
					ResourceType string `json:"resourceType"`
				} `json:"resInfo"`
				SrcEventId string `json:"srcEventId"`
			} `json:"data"`
		} `json:"events"`
	} `json:"params"`
}
