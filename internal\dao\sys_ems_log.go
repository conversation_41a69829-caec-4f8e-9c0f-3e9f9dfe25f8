// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysEmsLogDao is the data access object for the table hg_sys_ems_log.
// You can define custom methods on it to extend its functionality as needed.
type sysEmsLogDao struct {
	*internal.SysEmsLogDao
}

var (
	// SysEmsLog is a globally accessible object for table hg_sys_ems_log operations.
	SysEmsLog = sysEmsLogDao{internal.NewSysEmsLogDao()}
)

// Add your custom methods and functionality below.
