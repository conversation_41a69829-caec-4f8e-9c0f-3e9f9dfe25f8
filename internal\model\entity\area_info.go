// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AreaInfo is the golang structure for table area_info.
type AreaInfo struct {
	Id                int64       `json:"id"                orm:"id"                  description:""`
	AreaCode          string      `json:"areaCode"          orm:"area_code"           description:"区域编码"`
	AreaName          string      `json:"areaName"          orm:"area_name"           description:"区域名称"`
	ExternalAreaCode  string      `json:"externalAreaCode"  orm:"external_area_code"  description:"外部系统房间编码"`
	ExternalSystemUrl string      `json:"externalSystemUrl" orm:"external_system_url" description:"外部系统API地址"`
	Status            int         `json:"status"            orm:"status"              description:"状态:1启用,2禁用"`
	Remark            string      `json:"remark"            orm:"remark"              description:"备注"`
	CreatedBy         int64       `json:"createdBy"         orm:"created_by"          description:"创建者"`
	UpdatedBy         int64       `json:"updatedBy"         orm:"updated_by"          description:"更新者"`
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"          description:""`
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"          description:""`
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"          description:"删除时间"`
	Desc              string      `json:"desc"              orm:"desc"                description:"描述"`
}
