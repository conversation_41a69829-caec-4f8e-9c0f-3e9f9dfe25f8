// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AreaInfo is the golang structure of table hg_area_info for DAO operations like Where/Data.
type AreaInfo struct {
	g.Meta            `orm:"table:hg_area_info, do:true"`
	Id                interface{} //
	AreaCode          interface{} // 区域编码
	AreaName          interface{} // 区域名称
	ExternalAreaCode  interface{} // 外部系统房间编码
	ExternalSystemUrl interface{} // 外部系统API地址
	Status            interface{} // 状态:1启用,2禁用
	Remark            interface{} // 备注
	CreatedBy         interface{} // 创建者
	UpdatedBy         interface{} // 更新者
	CreatedAt         *gtime.Time //
	UpdatedAt         *gtime.Time //
	DeletedAt         *gtime.Time // 删除时间
	Desc              interface{} // 描述
}
