// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/areainfo"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	AreaInfo = cAreaInfo{}
)

type cAreaInfo struct{}

// List 查看区域管理列表
func (c *cAreaInfo) List(ctx context.Context, req *areainfo.ListReq) (res *areainfo.ListRes, err error) {
	list, totalCount, err := service.SysAreaInfo().List(ctx, &req.AreaInfoListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.AreaInfoListModel{}
	}

	res = new(areainfo.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Edit 更新区域管理
func (c *cAreaInfo) Edit(ctx context.Context, req *areainfo.EditReq) (res *areainfo.EditRes, err error) {
	err = service.SysAreaInfo().Edit(ctx, &req.AreaInfoEditInp)
	return
}

// View 获取指定区域管理信息
func (c *cAreaInfo) View(ctx context.Context, req *areainfo.ViewReq) (res *areainfo.ViewRes, err error) {
	data, err := service.SysAreaInfo().View(ctx, &req.AreaInfoViewInp)
	if err != nil {
		return
	}

	res = new(areainfo.ViewRes)
	res.AreaInfoViewModel = data
	return
}

// Delete 删除区域管理
func (c *cAreaInfo) Delete(ctx context.Context, req *areainfo.DeleteReq) (res *areainfo.DeleteRes, err error) {
	err = service.SysAreaInfo().Delete(ctx, &req.AreaInfoDeleteInp)
	return
}

// Status 更新区域管理状态
func (c *cAreaInfo) Status(ctx context.Context, req *areainfo.StatusReq) (res *areainfo.StatusRes, err error) {
	err = service.SysAreaInfo().Status(ctx, &req.AreaInfoStatusInp)
	return
}