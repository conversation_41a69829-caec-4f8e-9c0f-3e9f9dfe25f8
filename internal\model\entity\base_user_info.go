// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// BaseUserInfo is the golang structure for table base_user_info.
type BaseUserInfo struct {
	Id        int64       `json:"id"        orm:"id"         description:"ID"`
	Name      string      `json:"name"      orm:"name"       description:"姓名"`
	Sex       int         `json:"sex"       orm:"sex"        description:"性别"`
	Mobile    string      `json:"mobile"    orm:"mobile"     description:"手机号码"`
	IdCard    string      `json:"idCard"    orm:"id_card"    description:"身份证"`
	WorkUnit  string      `json:"workUnit"  orm:"work_unit"  description:"工作单位"`
	ShowPhoto string      `json:"showPhoto" orm:"show_photo" description:"展示图片"`
	SnapPhoto string      `json:"snapPhoto" orm:"snap_photo" description:"现场抓拍"`
	FacePhoto string      `json:"facePhoto" orm:"face_photo" description:"人脸识别"`
	Status    int         `json:"status"    orm:"status"     description:"状态"`
	CreatedBy int64       `json:"createdBy" orm:"created_by" description:"创建者"`
	UpdatedBy int64       `json:"updatedBy" orm:"updated_by" description:"更新者"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"修改时间"`
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"删除时间"`
}
