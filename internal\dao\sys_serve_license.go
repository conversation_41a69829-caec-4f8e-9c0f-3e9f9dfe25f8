// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysServeLicenseDao is the data access object for the table hg_sys_serve_license.
// You can define custom methods on it to extend its functionality as needed.
type sysServeLicenseDao struct {
	*internal.SysServeLicenseDao
}

var (
	// SysServeLicense is a globally accessible object for table hg_sys_serve_license operations.
	SysServeLicense = sysServeLicenseDao{internal.NewSysServeLicenseDao()}
)

// Add your custom methods and functionality below.
