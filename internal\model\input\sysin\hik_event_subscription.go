// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// HikEventSubscriptionUpdateFields 修改海康事件订阅表字段过滤
type HikEventSubscriptionUpdateFields struct {
	Ability         string      `json:"ability"         dc:"事件类别"`
	EventId         string      `json:"eventId"         dc:"事件唯一标识"`
	EventType       int         `json:"eventType"       dc:"事件类型"`
	SrcIndex        string      `json:"srcIndex"        dc:"事件源编号"`
	SrcType         string      `json:"srcType"         dc:"事件源类型"`
	FaceUrl         string      `json:"faceUrl"         dc:"人脸图片URI"`
	FaceTime        string      `json:"faceTime"        dc:"抓拍图片的时间"`
	CertificateType string      `json:"certificateType" dc:"目标对应的人脸的证件类型"`
	Certificate     string      `json:"certificate"     dc:"目标对应的人脸的证件号码"`
	FacePicUrl      string      `json:"facePicUrl"      dc:"目标人脸的图片"`
	HappenTime      *gtime.Time `json:"happenTime"      dc:"事件发生时间"`
	SendTime        *gtime.Time `json:"sendTime"        dc:"发送时间"`
	FaceInfoSex     string      `json:"faceInfoSex"     dc:"性别"`
	FaceInfoName    string      `json:"faceInfoName"    dc:"用户信息 (姓名_性别_)"`
	SrcName         string      `json:"srcName"         dc:"工作单位"`
	ResIndexCode    string      `json:"resIndexCode"    dc:"资源的唯一标识"`
	ResCn           string      `json:"resCn"           dc:"资源的名称"`
	ResResourceType string      `json:"resResourceType" dc:"资源类型"`
}

// HikEventSubscriptionInsertFields 新增海康事件订阅表字段过滤
type HikEventSubscriptionInsertFields struct {
	Ability         string      `json:"ability"         dc:"事件类别"`
	EventId         string      `json:"eventId"         dc:"事件唯一标识"`
	EventType       int         `json:"eventType"       dc:"事件类型"`
	SrcIndex        string      `json:"srcIndex"        dc:"事件源编号"`
	SrcType         string      `json:"srcType"         dc:"事件源类型"`
	FaceUrl         string      `json:"faceUrl"         dc:"人脸图片URI"`
	FaceTime        string      `json:"faceTime"        dc:"抓拍图片的时间"`
	CertificateType string      `json:"certificateType" dc:"目标对应的人脸的证件类型"`
	Certificate     string      `json:"certificate"     dc:"目标对应的人脸的证件号码"`
	FacePicUrl      string      `json:"facePicUrl"      dc:"目标人脸的图片"`
	HappenTime      *gtime.Time `json:"happenTime"      dc:"事件发生时间"`
	SendTime        *gtime.Time `json:"sendTime"        dc:"发送时间"`
	FaceInfoSex     string      `json:"faceInfoSex"     dc:"性别"`
	FaceInfoName    string      `json:"faceInfoName"    dc:"用户信息 (姓名_性别_)"`
	SrcName         string      `json:"srcName"         dc:"工作单位"`
	ResIndexCode    string      `json:"resIndexCode"    dc:"资源的唯一标识"`
	ResCn           string      `json:"resCn"           dc:"资源的名称"`
	ResResourceType string      `json:"resResourceType" dc:"资源类型"`
}

// HikEventSubscriptionEditInp 修改/新增海康事件订阅表
type HikEventSubscriptionEditInp struct {
	entity.HikEventSubscription
}

func (in *HikEventSubscriptionEditInp) Filter(ctx context.Context) (err error) {

	return
}

type HikEventSubscriptionEditModel struct{}

// HikEventSubscriptionDeleteInp 删除海康事件订阅表
type HikEventSubscriptionDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *HikEventSubscriptionDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type HikEventSubscriptionDeleteModel struct{}

// HikEventSubscriptionViewInp 获取指定海康事件订阅表信息
type HikEventSubscriptionViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *HikEventSubscriptionViewInp) Filter(ctx context.Context) (err error) {
	return
}

type HikEventSubscriptionViewModel struct {
	entity.HikEventSubscription
}

// HikEventSubscriptionListInp 获取海康事件订阅表列表
type HikEventSubscriptionListInp struct {
	form.PageReq
	Id int64 `json:"id" dc:"ID"`
}

func (in *HikEventSubscriptionListInp) Filter(ctx context.Context) (err error) {
	return
}

type HikEventSubscriptionListModel struct {
	Id              int64       `json:"id"              dc:"ID"`
	Ability         string      `json:"ability"         dc:"事件类别"`
	EventId         string      `json:"eventId"         dc:"事件唯一标识"`
	EventType       int         `json:"eventType"       dc:"事件类型"`
	SrcIndex        string      `json:"srcIndex"        dc:"事件源编号"`
	SrcType         string      `json:"srcType"         dc:"事件源类型"`
	FaceUrl         string      `json:"faceUrl"         dc:"人脸图片URI"`
	FaceTime        string      `json:"faceTime"        dc:"抓拍图片的时间"`
	CertificateType string      `json:"certificateType" dc:"目标对应的人脸的证件类型"`
	Certificate     string      `json:"certificate"     dc:"目标对应的人脸的证件号码"`
	FacePicUrl      string      `json:"facePicUrl"      dc:"目标人脸的图片"`
	HappenTime      *gtime.Time `json:"happenTime"      dc:"事件发生时间"`
	SendTime        *gtime.Time `json:"sendTime"        dc:"发送时间"`
	FaceInfoSex     string      `json:"faceInfoSex"     dc:"性别"`
	FaceInfoName    string      `json:"faceInfoName"    dc:"用户信息 (姓名_性别_)"`
	SrcName         string      `json:"srcName"         dc:"工作单位"`
	ResIndexCode    string      `json:"resIndexCode"    dc:"资源的唯一标识"`
	ResCn           string      `json:"resCn"           dc:"资源的名称"`
	ResResourceType string      `json:"resResourceType" dc:"资源类型"`
}

// HikEventSubscriptionExportModel 导出海康事件订阅表
type HikEventSubscriptionExportModel struct {
	Id              int64       `json:"id"              dc:"ID"`
	Ability         string      `json:"ability"         dc:"事件类别"`
	EventId         string      `json:"eventId"         dc:"事件唯一标识"`
	EventType       int         `json:"eventType"       dc:"事件类型"`
	SrcIndex        string      `json:"srcIndex"        dc:"事件源编号"`
	SrcType         string      `json:"srcType"         dc:"事件源类型"`
	FaceUrl         string      `json:"faceUrl"         dc:"人脸图片URI"`
	FaceTime        string      `json:"faceTime"        dc:"抓拍图片的时间"`
	CertificateType string      `json:"certificateType" dc:"目标对应的人脸的证件类型"`
	Certificate     string      `json:"certificate"     dc:"目标对应的人脸的证件号码"`
	FacePicUrl      string      `json:"facePicUrl"      dc:"目标人脸的图片"`
	HappenTime      *gtime.Time `json:"happenTime"      dc:"事件发生时间"`
	SendTime        *gtime.Time `json:"sendTime"        dc:"发送时间"`
	FaceInfoSex     string      `json:"faceInfoSex"     dc:"性别"`
	FaceInfoName    string      `json:"faceInfoName"    dc:"用户信息 (姓名_性别_)"`
	SrcName         string      `json:"srcName"         dc:"工作单位"`
	ResIndexCode    string      `json:"resIndexCode"    dc:"资源的唯一标识"`
	ResCn           string      `json:"resCn"           dc:"资源的名称"`
	ResResourceType string      `json:"resResourceType" dc:"资源类型"`
}
