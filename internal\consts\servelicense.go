// Package consts

package consts

import (
	"intellos/internal/library/dict"
	"intellos/internal/model"
)

func init() {
	dict.RegisterEnums("ServerLicenseGroupOptions", "服务授权分组选项", ServerLicenseGroupOptions)
}

// 授权分组
const (
	LicenseGroupDefault = "default" // 默认组
	LicenseGroupCron    = "cron"    // 定时任务
	LicenseGroupAuth    = "auth"    // 服务授权
)

// ServerLicenseGroupOptions 服务授权分组选项
var ServerLicenseGroupOptions = []*model.Option{
	dict.GenWarningOption(LicenseGroupDefault, "默认组"),
	dict.GenSuccessOption(LicenseGroupCron, "定时任务"),
	dict.GenSuccessOption(LicenseGroupAuth, "服务授权"),
}
