package consts

// 数据同步状态
const (
	SyncStatusPending    = 0 // 待同步
	SyncStatusProcessing = 1 // 同步中
	SyncStatusSuccess    = 2 // 同步成功
	SyncStatusFailed     = 3 // 同步失败
)

// 同步状态切片（用于验证）
var SyncStatusSlice = []int{
	SyncStatusPending,
	SyncStatusProcessing,
	SyncStatusSuccess,
	SyncStatusFailed,
}

// 最大重试次数
const MaxSyncRetryCount = 3

// 同步状态描述
var SyncStatusMap = map[int]string{
	SyncStatusPending:    "待同步",
	SyncStatusProcessing: "同步中",
	SyncStatusSuccess:    "同步成功",
	SyncStatusFailed:     "同步失败",
}
