// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// BaseUserInfoUpdateFields 修改人员列表字段过滤
type BaseUserInfoUpdateFields struct {
	Name      string `json:"name"      dc:"姓名"`
	Sex       int    `json:"sex"       dc:"性别"`
	Mobile    string `json:"mobile"    dc:"手机号码"`
	IdCard    string `json:"idCard"    dc:"身份证"`
	WorkUnit  string `json:"workUnit"  dc:"工作单位"`
	ShowPhoto string `json:"showPhoto" dc:"展示图片"`
	SnapPhoto string `json:"snapPhoto" dc:"现场抓拍"`
	FacePhoto string `json:"facePhoto" dc:"人脸识别"`
	Status    string `json:"status"    dc:"状态"`
	UpdatedBy int64  `json:"updatedBy" dc:"更新者"`
}

// BaseUserInfoInsertFields 新增人员列表字段过滤
type BaseUserInfoInsertFields struct {
	Name      string `json:"name"      dc:"姓名"`
	Sex       int    `json:"sex"       dc:"性别"`
	Mobile    string `json:"mobile"    dc:"手机号码"`
	IdCard    string `json:"idCard"    dc:"身份证"`
	WorkUnit  string `json:"workUnit"  dc:"工作单位"`
	ShowPhoto string `json:"showPhoto" dc:"展示图片"`
	SnapPhoto string `json:"snapPhoto" dc:"现场抓拍"`
	FacePhoto string `json:"facePhoto" dc:"人脸识别"`
	Status    string `json:"status"    dc:"状态"`
	CreatedBy int64  `json:"createdBy" dc:"创建者"`
}

// BaseUserInfoEditInp 修改/新增人员列表
type BaseUserInfoEditInp struct {
	entity.BaseUserInfo
}

func (in *BaseUserInfoEditInp) Filter(ctx context.Context) (err error) {
	// 验证身份证
	if err := g.Validator().Rules("resident-id").Data(in.IdCard).Messages("身份证不是身份证号码").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type BaseUserInfoEditModel struct{}

// BaseUserInfoDeleteInp 删除人员列表
type BaseUserInfoDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *BaseUserInfoDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type BaseUserInfoDeleteModel struct{}

// BaseUserInfoViewInp 获取指定人员列表信息
type BaseUserInfoViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *BaseUserInfoViewInp) Filter(ctx context.Context) (err error) {
	return
}

type BaseUserInfoViewModel struct {
	entity.BaseUserInfo
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// BaseUserInfoListInp 获取人员列表列表
type BaseUserInfoListInp struct {
	form.PageReq
	Name   string `json:"name"   dc:"姓名"`
	IdCard string `json:"idCard" dc:"身份证"`
	Status string `json:"status" dc:"状态"`
}

func (in *BaseUserInfoListInp) Filter(ctx context.Context) (err error) {
	return
}

type BaseUserInfoListModel struct {
	Id             int64             `json:"id"             dc:"ID"`
	Name           string            `json:"name"           dc:"姓名"`
	Sex            int               `json:"sex"            dc:"性别"`
	Mobile         string            `json:"mobile"         dc:"手机号码"`
	IdCard         string            `json:"idCard"         dc:"身份证"`
	WorkUnit       string            `json:"workUnit"       dc:"工作单位"`
	ShowPhoto      string            `json:"showPhoto"      dc:"展示图片"`
	SnapPhoto      string            `json:"snapPhoto"      dc:"现场抓拍"`
	FacePhoto      string            `json:"facePhoto"      dc:"人脸识别"`
	Status         string            `json:"status"         dc:"状态"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBy      int64             `json:"updatedBy"      dc:"更新者"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
	CreatedAt      *gtime.Time       `json:"createdAt"      dc:"创建时间"`
	UpdatedAt      *gtime.Time       `json:"updatedAt"      dc:"修改时间"`
}

// BaseUserInfoExportModel 导出人员列表
type BaseUserInfoExportModel struct {
	Id        int64       `json:"id"        dc:"ID"`
	Name      string      `json:"name"      dc:"姓名"`
	Sex       int         `json:"sex"       dc:"性别"`
	Mobile    string      `json:"mobile"    dc:"手机号码"`
	IdCard    string      `json:"idCard"    dc:"身份证"`
	WorkUnit  string      `json:"workUnit"  dc:"工作单位"`
	ShowPhoto string      `json:"showPhoto" dc:"展示图片"`
	SnapPhoto string      `json:"snapPhoto" dc:"现场抓拍"`
	FacePhoto string      `json:"facePhoto" dc:"人脸识别"`
	Status    string      `json:"status"    dc:"状态"`
	CreatedBy int64       `json:"createdBy" dc:"创建者"`
	UpdatedBy int64       `json:"updatedBy" dc:"更新者"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" dc:"修改时间"`
}

// BaseUserInfoStatusInp 更新人员列表状态
type BaseUserInfoStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *BaseUserInfoStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type BaseUserInfoStatusModel struct{}