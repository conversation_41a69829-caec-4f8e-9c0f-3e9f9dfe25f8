// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sSysAreaInfo struct{}

func NewSysAreaInfo() *sSysAreaInfo {
	return &sSysAreaInfo{}
}

func init() {
	service.RegisterSysAreaInfo(NewSysAreaInfo())
}

// Model 区域管理ORM模型
func (s *sSysAreaInfo) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.AreaInfo.Ctx(ctx), option...)
}

// List 获取区域管理列表
func (s *sSysAreaInfo) List(ctx context.Context, in *sysin.AreaInfoListInp) (list []*sysin.AreaInfoListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.AreaInfoListModel{})

	// 查询id
	if in.Id > 0 {
		mod = mod.Where(dao.AreaInfo.Columns().Id, in.Id)
	}

	// 查询区域编码
	if in.AreaCode != "" {
		mod = mod.WhereLike(dao.AreaInfo.Columns().AreaCode, in.AreaCode)
	}

	// 查询区域名称
	if in.AreaName != "" {
		mod = mod.WhereLike(dao.AreaInfo.Columns().AreaName, in.AreaName)
	}

	// 查询状态:1启用,2禁用
	if in.Status > 0 {
		mod = mod.Where(dao.AreaInfo.Columns().Status, in.Status)
	}

	// 查询描述
	if in.Desc != "" {
		mod = mod.WhereLike(dao.AreaInfo.Columns().Desc, in.Desc)
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.AreaInfo.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取区域管理列表失败，请稍后重试！")
		return
	}
	return
}

// Edit 修改/新增区域管理
func (s *sSysAreaInfo) Edit(ctx context.Context, in *sysin.AreaInfoEditInp) (err error) {
	// 验证'AreaCode'唯一
	if err = hgorm.IsUnique(ctx, &dao.AreaInfo, g.Map{dao.AreaInfo.Columns().AreaCode: in.AreaCode}, "区域编码已存在", in.Id); err != nil {
		return
	}
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.AreaInfoUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改区域管理失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.AreaInfoInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增区域管理失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除区域管理
func (s *sSysAreaInfo) Delete(ctx context.Context, in *sysin.AreaInfoDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除区域管理失败，请稍后重试！")
		return
	}
	return
}

// View 获取区域管理指定信息
func (s *sSysAreaInfo) View(ctx context.Context, in *sysin.AreaInfoViewInp) (res *sysin.AreaInfoViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取区域管理信息，请稍后重试！")
		return
	}
	return
}

// Status 更新区域管理状态
func (s *sSysAreaInfo) Status(ctx context.Context, in *sysin.AreaInfoStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.AreaInfo.Columns().Status:    in.Status,
		dao.AreaInfo.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新区域管理状态失败，请稍后重试！")
		return
	}
	return
}