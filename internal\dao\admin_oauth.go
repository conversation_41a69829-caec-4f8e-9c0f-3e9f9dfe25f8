// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminOauthDao is the data access object for the table hg_admin_oauth.
// You can define custom methods on it to extend its functionality as needed.
type adminOauthDao struct {
	*internal.AdminOauthDao
}

var (
	// AdminOauth is a globally accessible object for table hg_admin_oauth operations.
	AdminOauth = adminOauthDao{internal.NewAdminOauthDao()}
)

// Add your custom methods and functionality below.
