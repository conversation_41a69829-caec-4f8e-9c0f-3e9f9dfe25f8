// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminCreditsLogDao is the data access object for the table hg_admin_credits_log.
// You can define custom methods on it to extend its functionality as needed.
type adminCreditsLogDao struct {
	*internal.AdminCreditsLogDao
}

var (
	// AdminCreditsLog is a globally accessible object for table hg_admin_credits_log operations.
	AdminCreditsLog = adminCreditsLogDao{internal.NewAdminCreditsLogDao()}
)

// Add your custom methods and functionality below.
