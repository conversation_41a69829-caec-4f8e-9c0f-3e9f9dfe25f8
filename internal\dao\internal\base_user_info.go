// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BaseUserInfoDao is the data access object for the table hg_base_user_info.
type BaseUserInfoDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  BaseUserInfoColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// BaseUserInfoColumns defines and stores column names for the table hg_base_user_info.
type BaseUserInfoColumns struct {
	Id        string // ID
	Name      string // 姓名
	Sex       string // 性别
	Mobile    string // 手机号码
	IdCard    string // 身份证
	WorkUnit  string // 工作单位
	ShowPhoto string // 展示图片
	SnapPhoto string // 现场抓拍
	FacePhoto string // 人脸识别
	Status    string // 状态
	CreatedBy string // 创建者
	UpdatedBy string // 更新者
	CreatedAt string // 创建时间
	UpdatedAt string // 修改时间
	DeletedAt string // 删除时间
}

// baseUserInfoColumns holds the columns for the table hg_base_user_info.
var baseUserInfoColumns = BaseUserInfoColumns{
	Id:        "id",
	Name:      "name",
	Sex:       "sex",
	Mobile:    "mobile",
	IdCard:    "id_card",
	WorkUnit:  "work_unit",
	ShowPhoto: "show_photo",
	SnapPhoto: "snap_photo",
	FacePhoto: "face_photo",
	Status:    "status",
	CreatedBy: "created_by",
	UpdatedBy: "updated_by",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewBaseUserInfoDao creates and returns a new DAO object for table data access.
func NewBaseUserInfoDao(handlers ...gdb.ModelHandler) *BaseUserInfoDao {
	return &BaseUserInfoDao{
		group:    "default",
		table:    "hg_base_user_info",
		columns:  baseUserInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *BaseUserInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *BaseUserInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *BaseUserInfoDao) Columns() BaseUserInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *BaseUserInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *BaseUserInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *BaseUserInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
