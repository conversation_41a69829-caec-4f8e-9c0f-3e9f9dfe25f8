// Package sys

package sys

import (
	"context"
	"intellos/api/admin/smslog"
	"intellos/internal/service"
)

var (
	SmsLog = cSmsLog{}
)

type cSmsLog struct{}

// Delete 删除
func (c *cSmsLog) Delete(ctx context.Context, req *smslog.DeleteReq) (res *smslog.DeleteRes, err error) {
	err = service.SysSmsLog().Delete(ctx, &req.SmsLogDeleteInp)
	return
}

// View 获取指定信息
func (c *cSmsLog) View(ctx context.Context, req *smslog.ViewReq) (res *smslog.ViewRes, err error) {
	data, err := service.SysSmsLog().View(ctx, &req.SmsLogViewInp)
	if err != nil {
		return
	}

	res = new(smslog.ViewRes)
	res.SmsLogViewModel = data
	return
}

// List 查看列表
func (c *cSmsLog) List(ctx context.Context, req *smslog.ListReq) (res *smslog.ListRes, err error) {
	list, totalCount, err := service.SysSmsLog().List(ctx, &req.SmsLogListInp)
	if err != nil {
		return
	}

	res = new(smslog.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}
