// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminMemberDao is the data access object for the table hg_admin_member.
// You can define custom methods on it to extend its functionality as needed.
type adminMemberDao struct {
	*internal.AdminMemberDao
}

var (
	// AdminMember is a globally accessible object for table hg_admin_member operations.
	AdminMember = adminMemberDao{internal.NewAdminMemberDao()}
)

// Add your custom methods and functionality below.
