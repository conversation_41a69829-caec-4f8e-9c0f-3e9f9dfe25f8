// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// StrategyConditionDao is the data access object for the table hg_strategy_condition.
type StrategyConditionDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  StrategyConditionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// StrategyConditionColumns defines and stores column names for the table hg_strategy_condition.
type StrategyConditionColumns struct {
	Id              string //
	StrategyId      string // 策略主表ID
	ConditionCode   string // 条件编码
	ConditionName   string // 条件名称
	ConditionType   string // 条件类型
	ConditionConfig string // 条件配置参数
	IsConfigurable  string // 是否可配置:1可配置,0写死
	IsEnabled       string // 是否启用
	SortOrder       string // 排序
	CreatedAt       string //
	UpdatedAt       string //
}

// strategyConditionColumns holds the columns for the table hg_strategy_condition.
var strategyConditionColumns = StrategyConditionColumns{
	Id:              "id",
	StrategyId:      "strategy_id",
	ConditionCode:   "condition_code",
	ConditionName:   "condition_name",
	ConditionType:   "condition_type",
	ConditionConfig: "condition_config",
	IsConfigurable:  "is_configurable",
	IsEnabled:       "is_enabled",
	SortOrder:       "sort_order",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewStrategyConditionDao creates and returns a new DAO object for table data access.
func NewStrategyConditionDao(handlers ...gdb.ModelHandler) *StrategyConditionDao {
	return &StrategyConditionDao{
		group:    "default",
		table:    "hg_strategy_condition",
		columns:  strategyConditionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *StrategyConditionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *StrategyConditionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *StrategyConditionDao) Columns() StrategyConditionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *StrategyConditionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *StrategyConditionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *StrategyConditionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
