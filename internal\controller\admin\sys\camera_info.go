// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/camerainfo"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	CameraInfo = cCameraInfo{}
)

type cCameraInfo struct{}

// List 查看摄像头管理列表
func (c *cCameraInfo) List(ctx context.Context, req *camerainfo.ListReq) (res *camerainfo.ListRes, err error) {
	list, totalCount, err := service.SysCameraInfo().List(ctx, &req.CameraInfoListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.CameraInfoListModel{}
	}

	res = new(camerainfo.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Edit 更新摄像头管理
func (c *cCameraInfo) Edit(ctx context.Context, req *camerainfo.EditReq) (res *camerainfo.EditRes, err error) {
	err = service.SysCameraInfo().Edit(ctx, &req.CameraInfoEditInp)
	return
}

// View 获取指定摄像头管理信息
func (c *cCameraInfo) View(ctx context.Context, req *camerainfo.ViewReq) (res *camerainfo.ViewRes, err error) {
	data, err := service.SysCameraInfo().View(ctx, &req.CameraInfoViewInp)
	if err != nil {
		return
	}

	res = new(camerainfo.ViewRes)
	res.CameraInfoViewModel = data
	return
}

// Delete 删除摄像头管理
func (c *cCameraInfo) Delete(ctx context.Context, req *camerainfo.DeleteReq) (res *camerainfo.DeleteRes, err error) {
	err = service.SysCameraInfo().Delete(ctx, &req.CameraInfoDeleteInp)
	return
}

// Status 更新摄像头管理状态
func (c *cCameraInfo) Status(ctx context.Context, req *camerainfo.StatusReq) (res *camerainfo.StatusRes, err error) {
	err = service.SysCameraInfo().Status(ctx, &req.CameraInfoStatusInp)
	return
}