// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// HikEventSubscription is the golang structure of table hg_hik_event_subscription for DAO operations like Where/Data.
type HikEventSubscription struct {
	g.Meta          `orm:"table:hg_hik_event_subscription, do:true"`
	Id              interface{} // ID
	Ability         interface{} // 事件类别
	EventId         interface{} // 事件唯一标识
	EventType       interface{} // 事件类型
	SrcIndex        interface{} // 事件源编号
	SrcType         interface{} // 事件源类型
	FaceUrl         interface{} // 人脸图片URI
	FaceTime        interface{} // 抓拍图片的时间
	CertificateType interface{} // 目标对应的人脸的证件类型
	Certificate     interface{} // 目标对应的人脸的证件号码
	FacePicUrl      interface{} // 目标人脸的图片
	HappenTime      *gtime.Time // 事件发生时间
	SendTime        *gtime.Time // 发送时间
	FaceInfoSex     interface{} // 性别
	FaceInfoName    interface{} // 用户信息 (姓名_性别_)
	SrcName         interface{} // 工作单位
	ResIndexCode    interface{} // 资源的唯一标识
	ResCn           interface{} // 资源的名称
	ResResourceType interface{} // 资源类型
}
