// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// baseUserInfoDao is the data access object for the table hg_base_user_info.
// You can define custom methods on it to extend its functionality as needed.
type baseUserInfoDao struct {
	*internal.BaseUserInfoDao
}

var (
	// BaseUserInfo is a globally accessible object for table hg_base_user_info operations.
	BaseUserInfo = baseUserInfoDao{internal.NewBaseUserInfoDao()}
)

// Add your custom methods and functionality below.
