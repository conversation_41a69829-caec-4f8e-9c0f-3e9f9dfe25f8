// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/strategycondition"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	StrategyCondition = cStrategyCondition{}
)

type cStrategyCondition struct{}

// List 查看策略条件列表
func (c *cStrategyCondition) List(ctx context.Context, req *strategycondition.ListReq) (res *strategycondition.ListRes, err error) {
	list, totalCount, err := service.SysStrategyCondition().List(ctx, &req.StrategyConditionListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.StrategyConditionListModel{}
	}

	res = new(strategycondition.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出策略条件列表
func (c *cStrategyCondition) Export(ctx context.Context, req *strategycondition.ExportReq) (res *strategycondition.ExportRes, err error) {
	err = service.SysStrategyCondition().Export(ctx, &req.StrategyConditionListInp)
	return
}

// Edit 更新策略条件
func (c *cStrategyCondition) Edit(ctx context.Context, req *strategycondition.EditReq) (res *strategycondition.EditRes, err error) {
	err = service.SysStrategyCondition().Edit(ctx, &req.StrategyConditionEditInp)
	return
}

// View 获取指定策略条件信息
func (c *cStrategyCondition) View(ctx context.Context, req *strategycondition.ViewReq) (res *strategycondition.ViewRes, err error) {
	data, err := service.SysStrategyCondition().View(ctx, &req.StrategyConditionViewInp)
	if err != nil {
		return
	}

	res = new(strategycondition.ViewRes)
	res.StrategyConditionViewModel = data
	return
}

// Delete 删除策略条件
func (c *cStrategyCondition) Delete(ctx context.Context, req *strategycondition.DeleteReq) (res *strategycondition.DeleteRes, err error) {
	err = service.SysStrategyCondition().Delete(ctx, &req.StrategyConditionDeleteInp)
	return
}