## @{.label}

### 简介

@{.brief}


### 使用说明

@{.description}


### 迁移或安装

1、安装 intellos (@{.hgVersion}及以上)

项目介绍：https://github.com/bufanyun/intellos

2、将当前插件项目拷贝进 intellos 根目录的 server/addons 目录下

3、在 intellos 根目录的 server/addons/modules 目录下创建go文件:@{.name}.go，内容如下：
```go
package modules

import _ "intellos/addons/@{.name}"
```

4、intellos 后台进入 开发工具->插件管理->找到 @{.label} (@{.name}) 进行安装

5、重启服务即可生效


### 常用命令行

```shell
# 接口维护-gen service
gf gen service -s=addons/@{.name}/logic -d=addons/@{.name}/service

```
