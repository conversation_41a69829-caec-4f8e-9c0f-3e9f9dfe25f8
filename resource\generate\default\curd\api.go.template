// Package @{.varName | ToLower}
// @AutoGenerate Version @{.hgVersion}
//
package @{.varName | ToLower}

import (
	"github.com/gogf/gf/v2/frame/g"
	"@{.importInput}"
	"intellos/internal/model/input/form"
	@{ if eq .options.Step.IsTreeTable true }"intellos/utility/tree"@{end}
)

// ListReq 查询@{.tableComment}列表
type ListReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/list" method:"get" tags:"@{.tableComment}" summary:"获取@{.tableComment}列表"`
	@{.templateGroup}in.@{.varName}ListInp
}

type ListRes struct {
	form.PageRes
	List []*@{.templateGroup}in.@{.varName}ListModel `json:"list"   dc:"数据列表"`
}
@{ if eq .options.Step.HasExport true }
// ExportReq 导出@{.tableComment}列表
type ExportReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/export" method:"get" tags:"@{.tableComment}" summary:"导出@{.tableComment}列表"`
	@{.templateGroup}in.@{.varName}ListInp
}

type ExportRes struct{}
@{end}
@{ if or (eq .options.Step.HasView true) (eq .options.Step.HasEdit true) }
// ViewReq 获取@{.tableComment}指定信息
type ViewReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/view" method:"get" tags:"@{.tableComment}" summary:"获取@{.tableComment}指定信息"`
	@{.templateGroup}in.@{.varName}ViewInp
}

type ViewRes struct {
	*@{.templateGroup}in.@{.varName}ViewModel
}
@{end}
@{ if eq .options.Step.HasEdit true }
// EditReq 修改/新增@{.tableComment}
type EditReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/edit" method:"post" tags:"@{.tableComment}" summary:"修改/新增@{.tableComment}"`
	@{.templateGroup}in.@{.varName}EditInp
}

type EditRes struct{}
@{end}
@{ if eq .options.Step.HasDel true }
// DeleteReq 删除@{.tableComment}
type DeleteReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/delete" method:"post" tags:"@{.tableComment}" summary:"删除@{.tableComment}"`
	@{.templateGroup}in.@{.varName}DeleteInp
}

type DeleteRes struct{}
@{end}
@{ if and (eq .options.Step.HasEdit true) (eq .options.Step.HasMaxSort true) }
// MaxSortReq 获取@{.tableComment}最大排序
type MaxSortReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/maxSort" method:"get" tags:"@{.tableComment}" summary:"获取@{.tableComment}最大排序"`
	@{.templateGroup}in.@{.varName}MaxSortInp
}

type MaxSortRes struct {
	*@{.templateGroup}in.@{.varName}MaxSortModel
}
@{end}
@{ if eq .options.Step.HasStatus true }
// StatusReq 更新@{.tableComment}状态
type StatusReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/status" method:"post" tags:"@{.tableComment}" summary:"更新@{.tableComment}状态"`
	@{.templateGroup}in.@{.varName}StatusInp
}

type StatusRes struct{}
@{end}
@{ if eq .options.Step.HasSwitch true }
// SwitchReq 更新@{.tableComment}开关状态
type SwitchReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/switch" method:"post" tags:"@{.tableComment}" summary:"更新@{.tableComment}状态"`
	@{.templateGroup}in.@{.varName}SwitchInp
}

type SwitchRes struct{}
@{end}
@{ if eq .options.Step.IsTreeTable true }
// TreeOptionReq 获取@{.tableComment}关系树选项
type TreeOptionReq struct {
	g.Meta `path:"/@{.varName | LcFirst}/treeOption" method:"get" tags:"@{.tableComment}" summary:"获取@{.tableComment}关系树选项"`
}

type TreeOptionRes []tree.Node
@{end}