package strategy

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/internal/service"

	"intellos/api/api/strategy/v1"
)

func (c *ControllerV1) DDNotify(ctx context.Context, req *v1.DDNotifyReq) (res *v1.DDNotifyRes, err error) {
	g.Log().Infof(ctx, "收到调度系统新建项目同步通知：%s", gjson.MustEncodeString(req))
	//将数据保存到数据库
	if err := service.SysDdProjects().Edit(ctx, &req.DdProjectsEditInp); err != nil {
		return nil, err
	}
	return nil, nil
}
