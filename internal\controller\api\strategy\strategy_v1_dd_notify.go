package strategy

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"

	"intellos/api/api/strategy/v1"
)

func (c *ControllerV1) DDNotify(ctx context.Context, req *v1.DDNotifyReq) (res *v1.DDNotifyRes, err error) {
	g.Log().Infof(ctx, "收到调度系统新建项目同步通知：%s", gjson.MustEncodeString(req))
	editInp := sysin.DdProjectsEditInp{
		DdProjects: entity.DdProjects{
			RoomId:         req.RoomId,
			RoomName:       req.RoomName,
			BidCouncilId:   req.BidCouncilId,
			BidCouncilName: req.BidCouncilName,
			ProjectStatus:  req.ProjectStatus,
		},
	}
	//将数据保存到数据库
	if err := service.SysDdProjects().Edit(ctx, &editInp); err != nil {
		return nil, err
	}
	return nil, nil
}
