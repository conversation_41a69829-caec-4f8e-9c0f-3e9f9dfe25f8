// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DdProjectsDao is the data access object for the table hg_dd_projects.
type DdProjectsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  DdProjectsColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// DdProjectsColumns defines and stores column names for the table hg_dd_projects.
type DdProjectsColumns struct {
	Id             string // ID
	RoomId         string // 房间id
	RoomName       string // 房间名称
	BidCouncilId   string // 评委会id
	BidCouncilName string // 评委会名称
	ProjectStatus  string // 项目状态
	CreatedBy      string // 创建者
	UpdatedBy      string // 更新者
	CreatedAt      string // 创建时间
	UpdatedAt      string // 修改时间
	DeletedAt      string // 删除时间
	SyncStatus     string // 0待同步,1同步中,2同步成功,3同步失败
	SyncRetryCount string //
	SyncErrorMsg   string //
	LastSyncAt     string //
	ExtraData      string //
}

// ddProjectsColumns holds the columns for the table hg_dd_projects.
var ddProjectsColumns = DdProjectsColumns{
	Id:             "id",
	RoomId:         "room_id",
	RoomName:       "room_name",
	BidCouncilId:   "bid_council_id",
	BidCouncilName: "bid_council_name",
	ProjectStatus:  "project_status",
	CreatedBy:      "created_by",
	UpdatedBy:      "updated_by",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
	SyncStatus:     "sync_status",
	SyncRetryCount: "sync_retry_count",
	SyncErrorMsg:   "sync_error_msg",
	LastSyncAt:     "last_sync_at",
	ExtraData:      "extra_data",
}

// NewDdProjectsDao creates and returns a new DAO object for table data access.
func NewDdProjectsDao(handlers ...gdb.ModelHandler) *DdProjectsDao {
	return &DdProjectsDao{
		group:    "default",
		table:    "hg_dd_projects",
		columns:  ddProjectsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DdProjectsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DdProjectsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DdProjectsDao) Columns() DdProjectsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DdProjectsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DdProjectsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DdProjectsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
