// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminRoleMenuDao is the data access object for the table hg_admin_role_menu.
// You can define custom methods on it to extend its functionality as needed.
type adminRoleMenuDao struct {
	*internal.AdminRoleMenuDao
}

var (
	// AdminRoleMenu is a globally accessible object for table hg_admin_role_menu operations.
	AdminRoleMenu = adminRoleMenuDao{internal.NewAdminRoleMenuDao()}
)

// Add your custom methods and functionality below.
