// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/strategy"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	Strategy = cStrategy{}
)

type cStrategy struct{}

// List 查看策略配置列表
func (c *cStrategy) List(ctx context.Context, req *strategy.ListReq) (res *strategy.ListRes, err error) {
	list, totalCount, err := service.SysStrategy().List(ctx, &req.StrategyListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.StrategyListModel{}
	}

	res = new(strategy.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Edit 更新策略配置
func (c *cStrategy) Edit(ctx context.Context, req *strategy.EditReq) (res *strategy.EditRes, err error) {
	err = service.SysStrategy().Edit(ctx, &req.StrategyEditInp)
	return
}

//func (c *cStrategy) RelationEdit(ctx context.Context, req *strategy.RelationEditReq) (res *strategy.RelationEditRes, err error) {
//	err = service.SysStrategyRelation().RelationEdit(ctx, &req.StrategyRelationEditInp)
//	return
//}

// View 获取指定策略配置信息
func (c *cStrategy) View(ctx context.Context, req *strategy.ViewReq) (res *strategy.ViewRes, err error) {
	data, err := service.SysStrategy().View(ctx, &req.StrategyViewInp)
	if err != nil {
		return
	}

	res = new(strategy.ViewRes)
	res.StrategyViewModel = data
	return
}

// Delete 删除策略配置
func (c *cStrategy) Delete(ctx context.Context, req *strategy.DeleteReq) (res *strategy.DeleteRes, err error) {
	err = service.SysStrategy().Delete(ctx, &req.StrategyDeleteInp)
	return
}

// Status 更新策略配置状态
func (c *cStrategy) Status(ctx context.Context, req *strategy.StatusReq) (res *strategy.StatusRes, err error) {
	err = service.SysStrategy().Status(ctx, &req.StrategyStatusInp)
	return
}
