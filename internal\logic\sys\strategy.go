// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sSysStrategy struct{}

func NewSysStrategy() *sSysStrategy {
	return &sSysStrategy{}
}

func init() {
	service.RegisterSysStrategy(NewSysStrategy())
}

// Model 策略配置ORM模型
func (s *sSysStrategy) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.Strategy.Ctx(ctx), option...)
}

// List 获取策略配置列表
func (s *sSysStrategy) List(ctx context.Context, in *sysin.StrategyListInp) (list []*sysin.StrategyListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.StrategyListModel{})

	// 查询id
	if in.Id > 0 {
		mod = mod.Where(dao.Strategy.Columns().Id, in.Id)
	}

	// 查询策略ID
	if in.StrategyCode != "" {
		mod = mod.WhereLike(dao.Strategy.Columns().StrategyCode, in.StrategyCode)
	}

	// 查询策略名称
	if in.StrategyName != "" {
		mod = mod.WhereLike(dao.Strategy.Columns().StrategyName, in.StrategyName)
	}

	// 查询状态:1启用,2禁用
	if in.Status > 0 {
		mod = mod.Where(dao.Strategy.Columns().Status, in.Status)
	}

	// 查询created_at
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.Strategy.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.Strategy.Columns().Id)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取策略配置列表失败，请稍后重试！")
		return
	}
	return
}

// Edit 修改/新增策略配置
func (s *sSysStrategy) Edit(ctx context.Context, in *sysin.StrategyEditInp) (err error) {
	// 验证'StrategyCode'唯一
	if err = hgorm.IsUnique(ctx, &dao.Strategy, g.Map{dao.Strategy.Columns().StrategyCode: in.StrategyCode}, "策略ID已存在", in.Id); err != nil {
		return
	}
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.StrategyUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改策略配置失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.StrategyInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增策略配置失败，请稍后重试！")
		}
		//新增默认条件
		if err = s.generateDefaultConditions(ctx, in); err != nil {
			err = gerror.Wrap(err, "生成默认条件失败，请稍后重试！")
			return
		}

		return
	})
}

// generateDefaultConditions 生成默认条件
func (s *sSysStrategy) generateDefaultConditions(ctx context.Context, in *sysin.StrategyEditInp) error {
	var conditions []*entity.StrategyCondition

	switch in.StrategyCode {
	case "CL001": // 同时离场
		conditions = []*entity.StrategyCondition{
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL001_C001",
				ConditionName:   "条件1-采集的人像不在对应的房间",
				ConditionType:   "fixed_logic",
				ConditionConfig: gjson.New(`{"description": "采集的人像不在对应的房间","is_fixed": true}`),
				IsConfigurable:  0,
				SortOrder:       1,
			},
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL001_C002",
				ConditionName:   "条件2-采集的人像不在本评标室",
				ConditionType:   "fixed_logic",
				ConditionConfig: gjson.New(`{"description": "数据来源人像身份证号所在区域不在该评标室","is_fixed": true}`),
				IsConfigurable:  0,
				SortOrder:       2,
			},
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL001_C003",
				ConditionName:   "条件3-不应在本房间内人数",
				ConditionType:   "person_count_check",
				ConditionConfig: gjson.New(`{"max_person_count": 2, "operator": ">="}`),
				IsConfigurable:  1,
				SortOrder:       3,
			},
		}
	case "CL002": // 超员
		conditions = []*entity.StrategyCondition{
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL002_C001",
				ConditionName:   "条件1-采集的评标室人数统计",
				ConditionType:   "room_count_check",
				ConditionConfig: gjson.New(`{"operator": "<","operator_options": [">","=","<"]}`),
				IsConfigurable:  1,
				SortOrder:       1,
			},
		}
	case "CL003": // 错进
		conditions = []*entity.StrategyCondition{
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL003_C001",
				ConditionName:   "条件1-采集的人像不在对应的评标室",
				ConditionType:   "fixed_logic",
				ConditionConfig: gjson.New(`{"description": "数据来源区域身份证号不属于调度系统应有人员身份证号", "is_fixed": true}`),
				IsConfigurable:  0,
				SortOrder:       1,
			},
		}
	case "CL004": // 禁入
		conditions = []*entity.StrategyCondition{
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL004_C001",
				ConditionName:   "条件1-时间限制",
				ConditionType:   "time_restriction",
				ConditionConfig: gjson.New(`{"custom_time_enabled": false,"custom_time_config": {"end_date": "","start_date": ""},"weekday_config": {"enabled_weekdays": [{"name": "星期一","code": "1","isChecked": "false"},{"name": "星期二","code": "2","isChecked": "false"},{"name": "星期三","code": "3","isChecked": "false"},{"name": "星期四","code": "4","isChecked": "false"},{"name": "星期五","code": "5","isChecked": "false"},{"name": "星期六","code": "6","isChecked": "false"},{"name": "星期日","code": "7","isChecked": "false"}]}}`),
				IsConfigurable:  1,
				SortOrder:       1,
			},
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL004_C002",
				ConditionName:   "条件2-禁入的房间",
				ConditionType:   "room_restriction",
				ConditionConfig: gjson.New(`{"forbidden_rooms": [], "dict_code": "dd_room_code"`),
				IsConfigurable:  1,
				SortOrder:       2,
			},
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL004_C003",
				ConditionName:   "条件3-禁入人员",
				ConditionType:   "person_restriction",
				ConditionConfig: gjson.New(`{"person_list": [{"name":"张三","id_card":"123456789012345678"}]}`),
				IsConfigurable:  1,
				SortOrder:       3,
			},
			{
				StrategyId:      in.Id,
				ConditionCode:   "CL004_C004",
				ConditionName:   "条件4-禁入区域身份证检查",
				ConditionType:   "fixed_text",
				ConditionConfig: gjson.New(`{"fixed_text": "禁入区域的人员身份证号包含数据来源人员身份证号", "is_readonly": true}`),
				IsConfigurable:  0,
				SortOrder:       4,
			},
		}
	default:
		return gerror.New("未知的策略类型")
	}

	// 批量插入条件
	for _, condition := range conditions {
		_, err := dao.StrategyCondition.Ctx(ctx).Data(condition).Insert()
		if err != nil {
			return err
		}
	}

	return nil
}

// Delete 删除策略配置 及 策略关联的条件 todo 后续删除关联的 通知配置，未开发
func (s *sSysStrategy) Delete(ctx context.Context, in *sysin.StrategyDeleteInp) (err error) {

	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		//删除关联策略条件
		if _, err = dao.StrategyCondition.Ctx(ctx).Where(dao.StrategyCondition.Columns().StrategyId, in.Id).Unscoped().Delete(); err != nil {
			err = gerror.Wrap(err, "删除策略关联的策略条件失败，请稍后重试！")
			return
		}
		// 删除策略配置
		if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
			err = gerror.Wrap(err, "删除策略配置失败，请稍后重试！")
			return
		}

		return
	})

}

// View 获取策略配置指定信息
func (s *sSysStrategy) View(ctx context.Context, in *sysin.StrategyViewInp) (res *sysin.StrategyViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取策略配置信息，请稍后重试！")
		return
	}
	return
}

// Status 更新策略配置状态
func (s *sSysStrategy) Status(ctx context.Context, in *sysin.StrategyStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.Strategy.Columns().Status:    in.Status,
		dao.Strategy.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新策略配置状态失败，请稍后重试！")
		return
	}
	return
}

// RelationEdit 实现：修改/新增策略配置关联
func (s *sSysStrategy) RelationEdit(ctx context.Context, in *sysin.StrategyRelationEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		//处理区域关联 先删除，再保存
		if len(in.StrategyArea) > 0 {
			//先删除原来关联
			if _, err = dao.StrategyAreaRelation.Ctx(ctx).Where(dao.StrategyAreaRelation.Columns().StrategyId, in.StrategyArea[0].StrategyId).Delete(); err != nil {
				err = gerror.Wrap(err, "删除原有区域关联失败，请稍后重试！")
				return
			}
			for _, area := range in.StrategyArea {
				//验证区域是否存在
				if _, err = dao.AreaInfo.Ctx(ctx).WherePri(area.AreaId).One(); err != nil {
					err = gerror.Wrap(err, "区域不存在，请检查后重试！")
					return
				}
				//保存
				if _, err = dao.StrategyAreaRelation.Ctx(ctx).Insert(area); err != nil {
					err = gerror.Wrap(err, "保存区域关联失败，请稍后重试！")
					return
				}
			}
		}
		//处理策略条件关联 只uodate
		if len(in.StrategyCondition) > 0 {
			for _, condition := range in.StrategyCondition {
				//验证条件是否存在
				if _, err = dao.StrategyCondition.Ctx(ctx).WherePri(condition.Id).One(); err != nil {
					err = gerror.Wrap(err, "策略条件不存在，请检查后重试！")
					return
				}
				//更新
				if _, err = dao.StrategyCondition.Ctx(ctx).Data(condition).WherePri(condition.Id).Update(); err != nil {
					err = gerror.Wrap(err, "更新策略条件失败，请稍后重试！")
					return
				}
			}
		}
		return
	})
}

// CheckCameraStrategy 根据海康事件回调，处理摄像头策略
func (s *sSysStrategy) CheckCameraStrategy(ctx context.Context, in *sysin.HikEventSubscriptionEditInp) (err error) {
	//根据摄像头唯一id查询
	var camera *entity.CameraInfo
	if err = dao.CameraInfo.Ctx(ctx).Where(dao.CameraInfo.Columns().HikCode, in.ResIndexCode).Scan(&camera); err != nil {
		err = gerror.Wrap(err, "获取摄像头信息失败，请稍后重试！")
		return
	}

	//根据摄像头绑定的区域查询开启的策略
	mod := s.Model(ctx)
	mod = mod.Where(dao.Strategy.Columns().Status, 1) //启用状态
	mod = mod.LeftJoin(dao.StrategyAreaRelation.Table(), dao.Strategy.Columns().Id, dao.StrategyAreaRelation.Columns().StrategyId)
	mod = mod.Where(dao.StrategyAreaRelation.Columns().AreaId, camera.AreaId)
	var strategies []*entity.Strategy
	if err = mod.Scan(&strategies); err != nil {
		err = gerror.Wrap(err, "获取策略信息失败，请稍后重试！")
		return
	}
	//若查询到摄像头策略，进入处理策略逻辑
	return
}
