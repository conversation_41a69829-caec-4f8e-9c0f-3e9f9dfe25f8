// Package sysin

package sysin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UpdateAddonsConfigInp 更新指定插件的配置
type UpdateAddonsConfigInp struct {
	AddonName string `json:"addonName"`
	Group     string `json:"group"`
	List      g.Map  `json:"list"`
}

// GetAddonsConfigInp 获取指定插件的配置
type GetAddonsConfigInp struct {
	AddonName string `json:"addonName"`
	Group     string `json:"group"`
}
type GetAddonsConfigModel struct {
	List g.Map `json:"list"`
}
