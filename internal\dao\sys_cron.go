// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysCronDao is the data access object for the table hg_sys_cron.
// You can define custom methods on it to extend its functionality as needed.
type sysCronDao struct {
	*internal.SysCronDao
}

var (
	// SysCron is a globally accessible object for table hg_sys_cron operations.
	SysCron = sysCronDao{internal.NewSysCronDao()}
)

// Add your custom methods and functionality below.
