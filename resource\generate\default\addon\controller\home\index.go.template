// Package home

package home

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/addons/@{.name}/api/home/<USER>"
	"intellos/addons/@{.name}/service"
	"intellos/internal/model"
	isc "intellos/internal/service"
)

// Index 基础
var Index = cIndex{}

type cIndex struct{}

func (a *cIndex) Index(ctx context.Context, req *index.TestReq) (res *index.TestRes, err error) {
	data, err := service.SysIndex().Test(ctx, &req.IndexTestInp)
	if err != nil {
		return
	}

	isc.View().RenderTpl(ctx, "home/index.html", model.View{Data: g.Map{
		"name":   data.Name,
		"module": data.Module,
		"time":   data.Time,
	}})
	return
}
