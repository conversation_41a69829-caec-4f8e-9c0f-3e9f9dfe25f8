// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysStrategyCondition struct{}

func NewSysStrategyCondition() *sSysStrategyCondition {
	return &sSysStrategyCondition{}
}

func init() {
	service.RegisterSysStrategyCondition(NewSysStrategyCondition())
}

// Model 策略条件ORM模型
func (s *sSysStrategyCondition) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.StrategyCondition.Ctx(ctx), option...)
}

// List 获取策略条件列表
func (s *sSysStrategyCondition) List(ctx context.Context, in *sysin.StrategyConditionListInp) (list []*sysin.StrategyConditionListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.StrategyConditionListModel{})

	// 查询id
	if in.Id > 0 {
		mod = mod.Where(dao.StrategyCondition.Columns().Id, in.Id)
	}

	// 查询条件编码
	if in.ConditionCode != "" {
		mod = mod.WhereLike(dao.StrategyCondition.Columns().ConditionCode, in.ConditionCode)
	}

	// 查询条件名称
	if in.ConditionName != "" {
		mod = mod.WhereLike(dao.StrategyCondition.Columns().ConditionName, in.ConditionName)
	}

	// 查询created_at
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.StrategyCondition.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.StrategyCondition.Columns().Id)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取策略条件列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出策略条件
func (s *sSysStrategyCondition) Export(ctx context.Context, in *sysin.StrategyConditionListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.StrategyConditionExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出策略条件-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.StrategyConditionExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增策略条件
func (s *sSysStrategyCondition) Edit(ctx context.Context, in *sysin.StrategyConditionEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			if _, err = s.Model(ctx).
				Fields(sysin.StrategyConditionUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改策略条件失败，请稍后重试！")
			}
			return
		}

		// 新增
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.StrategyConditionInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增策略条件失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除策略条件
func (s *sSysStrategyCondition) Delete(ctx context.Context, in *sysin.StrategyConditionDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除策略条件失败，请稍后重试！")
		return
	}
	return
}

// View 获取策略条件指定信息
func (s *sSysStrategyCondition) View(ctx context.Context, in *sysin.StrategyConditionViewInp) (res *sysin.StrategyConditionViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取策略条件信息，请稍后重试！")
		return
	}
	return
}
