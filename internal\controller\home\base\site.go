// Package base

package base

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/api/home/<USER>"
	"intellos/internal/consts"
	"intellos/internal/model"
	"intellos/internal/service"
	"intellos/utility/simple"
)

// Site 基础
var Site = cSite{}

type cSite struct{}

func (a *cSite) Index(ctx context.Context, _ *base.SiteIndexReq) (res *base.SiteIndexRes, err error) {
	service.View().Render(ctx, model.View{Data: g.Map{
		"name":    simple.AppName(ctx),
		"version": consts.VersionApp,
	}})

	// err = gerror.New("这是一个测试错误")
	// return

	// err = gerror.NewCode(gcode.New(10000, "这是一个测试自定义错误码错误", nil))
	// return

	// service.View().Error(ctx, gerror.New("这是一个允许被自定义格式的错误，默认和通用错误格式一致，你可以修改它"))
	// return
	return
}
