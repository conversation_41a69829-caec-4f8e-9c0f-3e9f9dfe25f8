// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// payLogDao is the data access object for the table hg_pay_log.
// You can define custom methods on it to extend its functionality as needed.
type payLogDao struct {
	*internal.PayLogDao
}

var (
	// PayLog is a globally accessible object for table hg_pay_log operations.
	PayLog = payLogDao{internal.NewPayLogDao()}
)

// Add your custom methods and functionality below.
