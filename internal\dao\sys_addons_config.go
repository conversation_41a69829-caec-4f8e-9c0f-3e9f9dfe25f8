// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysAddonsConfigDao is the data access object for the table hg_sys_addons_config.
// You can define custom methods on it to extend its functionality as needed.
type sysAddonsConfigDao struct {
	*internal.SysAddonsConfigDao
}

var (
	// SysAddonsConfig is a globally accessible object for table hg_sys_addons_config operations.
	SysAddonsConfig = sysAddonsConfigDao{internal.NewSysAddonsConfigDao()}
)

// Add your custom methods and functionality below.
