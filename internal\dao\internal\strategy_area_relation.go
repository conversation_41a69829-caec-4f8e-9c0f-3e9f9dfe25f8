// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// StrategyAreaRelationDao is the data access object for the table hg_strategy_area_relation.
type StrategyAreaRelationDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  StrategyAreaRelationColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// StrategyAreaRelationColumns defines and stores column names for the table hg_strategy_area_relation.
type StrategyAreaRelationColumns struct {
	Id         string //
	StrategyId string // 策略主表ID
	AreaId     string // 区域ID
	AreaName   string // 区域名称
	IsEnabled  string // 是否启用
	CreatedAt  string //
	UpdatedAt  string //
}

// strategyAreaRelationColumns holds the columns for the table hg_strategy_area_relation.
var strategyAreaRelationColumns = StrategyAreaRelationColumns{
	Id:         "id",
	StrategyId: "strategy_id",
	AreaId:     "area_id",
	AreaName:   "area_name",
	IsEnabled:  "is_enabled",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewStrategyAreaRelationDao creates and returns a new DAO object for table data access.
func NewStrategyAreaRelationDao(handlers ...gdb.ModelHandler) *StrategyAreaRelationDao {
	return &StrategyAreaRelationDao{
		group:    "default",
		table:    "hg_strategy_area_relation",
		columns:  strategyAreaRelationColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *StrategyAreaRelationDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *StrategyAreaRelationDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *StrategyAreaRelationDao) Columns() StrategyAreaRelationColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *StrategyAreaRelationDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *StrategyAreaRelationDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *StrategyAreaRelationDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
