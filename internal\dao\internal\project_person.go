// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ProjectPersonDao is the data access object for the table hg_project_person.
type ProjectPersonDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  ProjectPersonColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// ProjectPersonColumns defines and stores column names for the table hg_project_person.
type ProjectPersonColumns struct {
	Id                 string // ID
	ProjectId          string //
	BidCouncilId       string //
	IdCard             string // 身份证号
	Name               string // 姓名
	Phone              string // 手机号
	RoleId             string // 角色id
	VerifyCode         string //
	WorkOrg            string // 工作单位
	ReportTime         string //
	SignoutAt          string //
	Status             string // 1-已同步，2-在评标室，3-离开
	RoomId             string // 房间id
	Source             string //
	CommitmentPdfStamp string //
	CreatedBy          string // 创建者
	UpdatedBy          string // 更新者
	CreatedAt          string // 创建时间
	UpdatedAt          string // 修改时间
	DeletedAt          string // 删除时间
}

// projectPersonColumns holds the columns for the table hg_project_person.
var projectPersonColumns = ProjectPersonColumns{
	Id:                 "id",
	ProjectId:          "project_id",
	BidCouncilId:       "bid_council_id",
	IdCard:             "id_card",
	Name:               "name",
	Phone:              "phone",
	RoleId:             "role_id",
	VerifyCode:         "verify_code",
	WorkOrg:            "work_org",
	ReportTime:         "report_time",
	SignoutAt:          "signout_at",
	Status:             "status",
	RoomId:             "room_id",
	Source:             "source",
	CommitmentPdfStamp: "commitmentPdfStamp",
	CreatedBy:          "created_by",
	UpdatedBy:          "updated_by",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	DeletedAt:          "deleted_at",
}

// NewProjectPersonDao creates and returns a new DAO object for table data access.
func NewProjectPersonDao(handlers ...gdb.ModelHandler) *ProjectPersonDao {
	return &ProjectPersonDao{
		group:    "default",
		table:    "hg_project_person",
		columns:  projectPersonColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ProjectPersonDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ProjectPersonDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ProjectPersonDao) Columns() ProjectPersonColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ProjectPersonDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ProjectPersonDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ProjectPersonDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
