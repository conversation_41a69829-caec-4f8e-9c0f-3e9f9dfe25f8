// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysAttachmentDao is the data access object for the table hg_sys_attachment.
// You can define custom methods on it to extend its functionality as needed.
type sysAttachmentDao struct {
	*internal.SysAttachmentDao
}

var (
	// SysAttachment is a globally accessible object for table hg_sys_attachment operations.
	SysAttachment = sysAttachmentDao{internal.NewSysAttachmentDao()}
)

// Add your custom methods and functionality below.
