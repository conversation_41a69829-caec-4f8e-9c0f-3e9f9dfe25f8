// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// StrategyDao is the data access object for the table hg_strategy.
type StrategyDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  StrategyColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// StrategyColumns defines and stores column names for the table hg_strategy.
type StrategyColumns struct {
	Id           string //
	StrategyCode string // 策略编码
	StrategyName string // 策略名称
	DdRoomCode   string // 调度系统-区域
	Desc         string // 业务描述
	Status       string // 状态:1启用,2禁用
	Remark       string // 备注
	CreatedBy    string // 创建者
	UpdatedBy    string // 更新者
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string // 删除时间
}

// strategyColumns holds the columns for the table hg_strategy.
var strategyColumns = StrategyColumns{
	Id:           "id",
	StrategyCode: "strategy_code",
	StrategyName: "strategy_name",
	DdRoomCode:   "dd_room_code",
	Desc:         "desc",
	Status:       "status",
	Remark:       "remark",
	CreatedBy:    "created_by",
	UpdatedBy:    "updated_by",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewStrategyDao creates and returns a new DAO object for table data access.
func NewStrategyDao(handlers ...gdb.ModelHandler) *StrategyDao {
	return &StrategyDao{
		group:    "default",
		table:    "hg_strategy",
		columns:  strategyColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *StrategyDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *StrategyDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *StrategyDao) Columns() StrategyColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *StrategyDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *StrategyDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *StrategyDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
