// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminPostDao is the data access object for the table hg_admin_post.
// You can define custom methods on it to extend its functionality as needed.
type adminPostDao struct {
	*internal.AdminPostDao
}

var (
	// AdminPost is a globally accessible object for table hg_admin_post operations.
	AdminPost = adminPostDao{internal.NewAdminPostDao()}
)

// Add your custom methods and functionality below.
