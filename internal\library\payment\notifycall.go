// Package payment

package payment

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/internal/library/contexts"
	"intellos/internal/model/input/payin"
	"intellos/utility/simple"
	"sync"
)

// 异步回调

type NotifyCallFunc func(ctx context.Context, pay *payin.NotifyCallFuncInp) (err error)

var (
	notifyCall = make(map[string]NotifyCallFunc)
	ncLock     sync.Mutex
)

// RegisterNotifyCall 注册支付成功回调方法
func RegisterNotifyCall(group string, f NotifyCallFunc) {
	ncLock.Lock()
	defer ncLock.Unlock()
	if _, ok := notifyCall[group]; ok {
		panic("notifyCall repeat registration, group:" + group)
	}
	notifyCall[group] = f
}

// RegisterNotifyCallMap 注册支付成功回调方法
func RegisterNotifyCallMap(calls map[string]NotifyCallFunc) {
	for group, f := range calls {
		RegisterNotifyCall(group, f)
	}
}

// NotifyCall 执行订单分组的异步回调
func NotifyCall(ctx context.Context, in *payin.NotifyCallFuncInp) {
	f, ok := notifyCall[in.Pay.OrderGroup]
	if ok {
		ctx = contexts.Detach(ctx)
		simple.SafeGo(ctx, func(ctx context.Context) {
			if err := f(ctx, in); err != nil {
				g.Log().Warningf(ctx, "payment.NotifyCall in:%+v exec err:%+v", gjson.New(in.Pay).String(), err)
			}
		})
	}
}
