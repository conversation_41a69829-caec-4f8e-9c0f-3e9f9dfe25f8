// Package ddprojects
// @AutoGenerate Version 2.16.10
package ddprojects

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询调度系统同步最新评委会接口列表
type ListReq struct {
	g.Meta `path:"/ddProjects/list" method:"get" tags:"调度系统同步最新评委会接口" summary:"获取调度系统同步最新评委会接口列表"`
	sysin.DdProjectsListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.DdProjectsListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出调度系统同步最新评委会接口列表
type ExportReq struct {
	g.Meta `path:"/ddProjects/export" method:"get" tags:"调度系统同步最新评委会接口" summary:"导出调度系统同步最新评委会接口列表"`
	sysin.DdProjectsListInp
}

type ExportRes struct{}

// ViewReq 获取调度系统同步最新评委会接口指定信息
type ViewReq struct {
	g.Meta `path:"/ddProjects/view" method:"get" tags:"调度系统同步最新评委会接口" summary:"获取调度系统同步最新评委会接口指定信息"`
	sysin.DdProjectsViewInp
}

type ViewRes struct {
	*sysin.DdProjectsViewModel
}

// EditReq 修改/新增调度系统同步最新评委会接口
type EditReq struct {
	g.Meta `path:"/ddProjects/edit" method:"post" tags:"调度系统同步最新评委会接口" summary:"修改/新增调度系统同步最新评委会接口"`
	sysin.DdProjectsEditInp
}

type EditRes struct{}

// DeleteReq 删除调度系统同步最新评委会接口
type DeleteReq struct {
	g.Meta `path:"/ddProjects/delete" method:"post" tags:"调度系统同步最新评委会接口" summary:"删除调度系统同步最新评委会接口"`
	sysin.DdProjectsDeleteInp
}

type DeleteRes struct{}