// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyCondition is the golang structure for table strategy_condition.
type StrategyCondition struct {
	Id              int64       `json:"id"              orm:"id"               description:""`
	StrategyId      int64       `json:"strategyId"      orm:"strategy_id"      description:"策略主表ID"`
	ConditionCode   string      `json:"conditionCode"   orm:"condition_code"   description:"条件编码"`
	ConditionName   string      `json:"conditionName"   orm:"condition_name"   description:"条件名称"`
	ConditionType   string      `json:"conditionType"   orm:"condition_type"   description:"条件类型"`
	ConditionConfig *gjson.Json `json:"conditionConfig" orm:"condition_config" description:"条件配置参数"`
	IsConfigurable  int         `json:"isConfigurable"  orm:"is_configurable"  description:"是否可配置:1可配置,0写死"`
	IsEnabled       int         `json:"isEnabled"       orm:"is_enabled"       description:"是否启用"`
	SortOrder       int         `json:"sortOrder"       orm:"sort_order"       description:"排序"`
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:""`
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:""`
}
