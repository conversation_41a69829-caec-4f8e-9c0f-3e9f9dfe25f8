// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/strategyarearelation"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	StrategyAreaRelation = cStrategyAreaRelation{}
)

type cStrategyAreaRelation struct{}

// List 查看区域策略关联表列表
func (c *cStrategyAreaRelation) List(ctx context.Context, req *strategyarearelation.ListReq) (res *strategyarearelation.ListRes, err error) {
	list, totalCount, err := service.SysStrategyAreaRelation().List(ctx, &req.StrategyAreaRelationListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.StrategyAreaRelationListModel{}
	}

	res = new(strategyarearelation.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出区域策略关联表列表
func (c *cStrategyAreaRelation) Export(ctx context.Context, req *strategyarearelation.ExportReq) (res *strategyarearelation.ExportRes, err error) {
	err = service.SysStrategyAreaRelation().Export(ctx, &req.StrategyAreaRelationListInp)
	return
}

// Edit 更新区域策略关联表
func (c *cStrategyAreaRelation) Edit(ctx context.Context, req *strategyarearelation.EditReq) (res *strategyarearelation.EditRes, err error) {
	err = service.SysStrategyAreaRelation().Edit(ctx, &req.StrategyAreaRelationEditInp)
	return
}

// View 获取指定区域策略关联表信息
func (c *cStrategyAreaRelation) View(ctx context.Context, req *strategyarearelation.ViewReq) (res *strategyarearelation.ViewRes, err error) {
	data, err := service.SysStrategyAreaRelation().View(ctx, &req.StrategyAreaRelationViewInp)
	if err != nil {
		return
	}

	res = new(strategyarearelation.ViewRes)
	res.StrategyAreaRelationViewModel = data
	return
}

// Delete 删除区域策略关联表
func (c *cStrategyAreaRelation) Delete(ctx context.Context, req *strategyarearelation.DeleteReq) (res *strategyarearelation.DeleteRes, err error) {
	err = service.SysStrategyAreaRelation().Delete(ctx, &req.StrategyAreaRelationDeleteInp)
	return
}