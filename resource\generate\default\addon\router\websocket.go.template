// Package router

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"intellos/addons/@{.name}/controller/websocket"
	"intellos/addons/@{.name}/global"
	"intellos/internal/consts"
	"intellos/internal/library/addons"
	"intellos/internal/service"
	ws "intellos/internal/websocket"
)

// WebSocket ws路由配置
func WebSocket(ctx context.Context, group *ghttp.RouterGroup) {
	prefix := addons.RouterPrefix(ctx, consts.AppWebSocket, global.GetSkeleton().Name)
	group.Group(prefix, func(group *ghttp.RouterGroup) {
		group.Bind(
			// 无需验证的路由
			websocket.Index,
		)
		// ws连接中间件
		group.Middleware(service.Middleware().WebSocketAuth)
		group.Bind(
		// 需要验证的路由
		// ..
		)
	})

	// 注册消息路由
	ws.RegisterMsg(ws.EventHandlers{
		// ...
	})
}
