// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// addonHgexampleTenantOrderDao is the data access object for the table hg_addon_hgexample_tenant_order.
// You can define custom methods on it to extend its functionality as needed.
type addonHgexampleTenantOrderDao struct {
	*internal.AddonHgexampleTenantOrderDao
}

var (
	// AddonHgexampleTenantOrder is a globally accessible object for table hg_addon_hgexample_tenant_order operations.
	AddonHgexampleTenantOrder = addonHgexampleTenantOrderDao{internal.NewAddonHgexampleTenantOrderDao()}
)

// Add your custom methods and functionality below.
