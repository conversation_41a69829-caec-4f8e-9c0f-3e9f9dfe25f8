// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DdProjects is the golang structure of table hg_dd_projects for DAO operations like Where/Data.
type DdProjects struct {
	g.Meta         `orm:"table:hg_dd_projects, do:true"`
	Id             interface{} // ID
	RoomId         interface{} // 房间id
	RoomName       interface{} // 房间名称
	BidCouncilId   interface{} // 评委会id
	BidCouncilName interface{} // 评委会名称
	ProjectStatus  interface{} // 项目状态
	CreatedBy      interface{} // 创建者
	UpdatedBy      interface{} // 更新者
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 修改时间
	DeletedAt      *gtime.Time // 删除时间
	SyncStatus     interface{} // 0待同步,1同步中,2同步成功,3同步失败
	SyncRetryCount interface{} //
	SyncErrorMsg   interface{} //
	LastSyncAt     *gtime.Time //
	ExtraData      interface{} //
}
