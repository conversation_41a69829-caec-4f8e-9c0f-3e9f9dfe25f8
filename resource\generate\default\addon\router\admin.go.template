// Package router

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"intellos/addons/@{.name}/controller/admin/sys"
	"intellos/addons/@{.name}/global"
	"intellos/addons/@{.name}/router/genrouter"
	"intellos/internal/consts"
	"intellos/internal/library/addons"
	"intellos/internal/service"
)

// Admin 后台路由
func Admin(ctx context.Context, group *ghttp.RouterGroup) {
	prefix := addons.RouterPrefix(ctx, consts.AppAdmin, global.GetSkeleton().Name)
	group.Group(prefix, func(group *ghttp.RouterGroup) {
		group.Bind(
			sys.Index,
		)
		group.Middleware(service.Middleware().AdminAuth)
		group.Bind(
			sys.Config,
		)
	})

	// 注册生成路由
	genrouter.Register(ctx, group)
}
