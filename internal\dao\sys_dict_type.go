// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysDictTypeDao is the data access object for the table hg_sys_dict_type.
// You can define custom methods on it to extend its functionality as needed.
type sysDictTypeDao struct {
	*internal.SysDictTypeDao
}

var (
	// SysDictType is a globally accessible object for table hg_sys_dict_type operations.
	SysDictType = sysDictTypeDao{internal.NewSysDictTypeDao()}
)

// Add your custom methods and functionality below.
