// Package camerainfo
// @AutoGenerate Version 2.16.10
package camerainfo

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询摄像头管理列表
type ListReq struct {
	g.Meta `path:"/cameraInfo/list" method:"get" tags:"摄像头管理" summary:"获取摄像头管理列表"`
	sysin.CameraInfoListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.CameraInfoListModel `json:"list"   dc:"数据列表"`
}

// ViewReq 获取摄像头管理指定信息
type ViewReq struct {
	g.Meta `path:"/cameraInfo/view" method:"get" tags:"摄像头管理" summary:"获取摄像头管理指定信息"`
	sysin.CameraInfoViewInp
}

type ViewRes struct {
	*sysin.CameraInfoViewModel
}

// EditReq 修改/新增摄像头管理
type EditReq struct {
	g.Meta `path:"/cameraInfo/edit" method:"post" tags:"摄像头管理" summary:"修改/新增摄像头管理"`
	sysin.CameraInfoEditInp
}

type EditRes struct{}

// DeleteReq 删除摄像头管理
type DeleteReq struct {
	g.Meta `path:"/cameraInfo/delete" method:"post" tags:"摄像头管理" summary:"删除摄像头管理"`
	sysin.CameraInfoDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新摄像头管理状态
type StatusReq struct {
	g.Meta `path:"/cameraInfo/status" method:"post" tags:"摄像头管理" summary:"更新摄像头管理状态"`
	sysin.CameraInfoStatusInp
}

type StatusRes struct{}