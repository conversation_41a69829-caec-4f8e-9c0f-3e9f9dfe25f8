// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Strategy is the golang structure for table strategy.
type Strategy struct {
	Id           int64       `json:"id"           orm:"id"            description:""`
	StrategyCode string      `json:"strategyCode" orm:"strategy_code" description:"策略编码"`
	StrategyName string      `json:"strategyName" orm:"strategy_name" description:"策略名称"`
	DdRoomCode   string      `json:"ddRoomCode"   orm:"dd_room_code"  description:"调度系统-区域"`
	Desc         string      `json:"desc"         orm:"desc"          description:"业务描述"`
	Status       int         `json:"status"       orm:"status"        description:"状态:1启用,2禁用"`
	Remark       string      `json:"remark"       orm:"remark"        description:"备注"`
	CreatedBy    int64       `json:"createdBy"    orm:"created_by"    description:"创建者"`
	UpdatedBy    int64       `json:"updatedBy"    orm:"updated_by"    description:"更新者"`
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""`
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""`
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:"删除时间"`
}
