// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AreaInfoDao is the data access object for the table hg_area_info.
type AreaInfoDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AreaInfoColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AreaInfoColumns defines and stores column names for the table hg_area_info.
type AreaInfoColumns struct {
	Id                string //
	AreaCode          string // 区域编码
	AreaName          string // 区域名称
	ExternalAreaCode  string // 外部系统房间编码
	ExternalSystemUrl string // 外部系统API地址
	Status            string // 状态:1启用,2禁用
	Remark            string // 备注
	CreatedBy         string // 创建者
	UpdatedBy         string // 更新者
	CreatedAt         string //
	UpdatedAt         string //
	DeletedAt         string // 删除时间
	Desc              string // 描述
}

// areaInfoColumns holds the columns for the table hg_area_info.
var areaInfoColumns = AreaInfoColumns{
	Id:                "id",
	AreaCode:          "area_code",
	AreaName:          "area_name",
	ExternalAreaCode:  "external_area_code",
	ExternalSystemUrl: "external_system_url",
	Status:            "status",
	Remark:            "remark",
	CreatedBy:         "created_by",
	UpdatedBy:         "updated_by",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
	Desc:              "desc",
}

// NewAreaInfoDao creates and returns a new DAO object for table data access.
func NewAreaInfoDao(handlers ...gdb.ModelHandler) *AreaInfoDao {
	return &AreaInfoDao{
		group:    "default",
		table:    "hg_area_info",
		columns:  areaInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AreaInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AreaInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AreaInfoDao) Columns() AreaInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AreaInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AreaInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AreaInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
