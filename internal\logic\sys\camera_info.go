// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sSysCameraInfo struct{}

func NewSysCameraInfo() *sSysCameraInfo {
	return &sSysCameraInfo{}
}

func init() {
	service.RegisterSysCameraInfo(NewSysCameraInfo())
}

// Model 摄像头管理ORM模型
func (s *sSysCameraInfo) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.CameraInfo.Ctx(ctx), option...)
}

// List 获取摄像头管理列表
func (s *sSysCameraInfo) List(ctx context.Context, in *sysin.CameraInfoListInp) (list []*sysin.CameraInfoListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.CameraInfoListModel{})

	// 查询摄像头编码
	if in.CameraCode != "" {
		mod = mod.WhereLike(dao.CameraInfo.Columns().CameraCode, in.CameraCode)
	}

	// 查询摄像头名称
	if in.CameraName != "" {
		mod = mod.WhereLike(dao.CameraInfo.Columns().CameraName, in.CameraName)
	}

	// 查询状态:1启用,2禁用
	if in.Status > 0 {
		mod = mod.Where(dao.CameraInfo.Columns().Status, in.Status)
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.CameraInfo.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取摄像头管理列表失败，请稍后重试！")
		return
	}
	return
}

// Edit 修改/新增摄像头管理
func (s *sSysCameraInfo) Edit(ctx context.Context, in *sysin.CameraInfoEditInp) (err error) {
	// 验证'CameraCode'唯一
	if err = hgorm.IsUnique(ctx, &dao.CameraInfo, g.Map{dao.CameraInfo.Columns().CameraCode: in.CameraCode}, "摄像头编码已存在", in.Id); err != nil {
		return
	}
	// 验证'HikCode'唯一
	if err = hgorm.IsUnique(ctx, &dao.CameraInfo, g.Map{dao.CameraInfo.Columns().HikCode: in.HikCode}, "海康系统内唯一物理id已存在", in.Id); err != nil {
		return
	}
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.CameraInfoUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改摄像头管理失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.CameraInfoInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增摄像头管理失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除摄像头管理
func (s *sSysCameraInfo) Delete(ctx context.Context, in *sysin.CameraInfoDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除摄像头管理失败，请稍后重试！")
		return
	}
	return
}

// View 获取摄像头管理指定信息
func (s *sSysCameraInfo) View(ctx context.Context, in *sysin.CameraInfoViewInp) (res *sysin.CameraInfoViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取摄像头管理信息，请稍后重试！")
		return
	}
	return
}

// Status 更新摄像头管理状态
func (s *sSysCameraInfo) Status(ctx context.Context, in *sysin.CameraInfoStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.CameraInfo.Columns().Status:    in.Status,
		dao.CameraInfo.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新摄像头管理状态失败，请稍后重试！")
		return
	}
	return
}