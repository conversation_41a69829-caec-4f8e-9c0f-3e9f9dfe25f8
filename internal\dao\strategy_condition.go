// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// strategyConditionDao is the data access object for the table hg_strategy_condition.
// You can define custom methods on it to extend its functionality as needed.
type strategyConditionDao struct {
	*internal.StrategyConditionDao
}

var (
	// StrategyCondition is a globally accessible object for table hg_strategy_condition operations.
	StrategyCondition = strategyConditionDao{internal.NewStrategyConditionDao()}
)

// Add your custom methods and functionality below.
