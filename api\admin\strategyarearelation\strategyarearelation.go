// Package strategyarearelation
// @AutoGenerate Version 2.16.10
package strategyarearelation

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询区域策略关联表列表
type ListReq struct {
	g.Meta `path:"/strategyAreaRelation/list" method:"get" tags:"区域策略关联表" summary:"获取区域策略关联表列表"`
	sysin.StrategyAreaRelationListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.StrategyAreaRelationListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出区域策略关联表列表
type ExportReq struct {
	g.Meta `path:"/strategyAreaRelation/export" method:"get" tags:"区域策略关联表" summary:"导出区域策略关联表列表"`
	sysin.StrategyAreaRelationListInp
}

type ExportRes struct{}

// ViewReq 获取区域策略关联表指定信息
type ViewReq struct {
	g.Meta `path:"/strategyAreaRelation/view" method:"get" tags:"区域策略关联表" summary:"获取区域策略关联表指定信息"`
	sysin.StrategyAreaRelationViewInp
}

type ViewRes struct {
	*sysin.StrategyAreaRelationViewModel
}

// EditReq 修改/新增区域策略关联表
type EditReq struct {
	g.Meta `path:"/strategyAreaRelation/edit" method:"post" tags:"区域策略关联表" summary:"修改/新增区域策略关联表"`
	sysin.StrategyAreaRelationEditInp
}

type EditRes struct{}

// DeleteReq 删除区域策略关联表
type DeleteReq struct {
	g.Meta `path:"/strategyAreaRelation/delete" method:"post" tags:"区域策略关联表" summary:"删除区域策略关联表"`
	sysin.StrategyAreaRelationDeleteInp
}

type DeleteRes struct{}