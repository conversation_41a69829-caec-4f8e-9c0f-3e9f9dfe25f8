// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysGenTreeDemoDao is the data access object for the table hg_sys_gen_tree_demo.
// You can define custom methods on it to extend its functionality as needed.
type sysGenTreeDemoDao struct {
	*internal.SysGenTreeDemoDao
}

var (
	// SysGenTreeDemo is a globally accessible object for table hg_sys_gen_tree_demo operations.
	SysGenTreeDemo = sysGenTreeDemoDao{internal.NewSysGenTreeDemoDao()}
)

// Add your custom methods and functionality below.
