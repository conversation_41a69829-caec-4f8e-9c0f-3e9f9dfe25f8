// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// testCategoryDao is the data access object for the table hg_test_category.
// You can define custom methods on it to extend its functionality as needed.
type testCategoryDao struct {
	*internal.TestCategoryDao
}

var (
	// TestCategory is a globally accessible object for table hg_test_category operations.
	TestCategory = testCategoryDao{internal.NewTestCategoryDao()}
)

// Add your custom methods and functionality below.
