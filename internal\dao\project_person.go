// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// projectPersonDao is the data access object for the table hg_project_person.
// You can define custom methods on it to extend its functionality as needed.
type projectPersonDao struct {
	*internal.ProjectPersonDao
}

var (
	// ProjectPerson is a globally accessible object for table hg_project_person operations.
	ProjectPerson = projectPersonDao{internal.NewProjectPersonDao()}
)

// Add your custom methods and functionality below.
