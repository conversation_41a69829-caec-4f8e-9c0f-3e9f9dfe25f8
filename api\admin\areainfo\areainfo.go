// Package areainfo
// @AutoGenerate Version 2.16.10
package areainfo

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询区域管理列表
type ListReq struct {
	g.Meta `path:"/areaInfo/list" method:"get" tags:"区域管理" summary:"获取区域管理列表"`
	sysin.AreaInfoListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.AreaInfoListModel `json:"list"   dc:"数据列表"`
}

// ViewReq 获取区域管理指定信息
type ViewReq struct {
	g.Meta `path:"/areaInfo/view" method:"get" tags:"区域管理" summary:"获取区域管理指定信息"`
	sysin.AreaInfoViewInp
}

type ViewRes struct {
	*sysin.AreaInfoViewModel
}

// EditReq 修改/新增区域管理
type EditReq struct {
	g.Meta `path:"/areaInfo/edit" method:"post" tags:"区域管理" summary:"修改/新增区域管理"`
	sysin.AreaInfoEditInp
}

type EditRes struct{}

// DeleteReq 删除区域管理
type DeleteReq struct {
	g.Meta `path:"/areaInfo/delete" method:"post" tags:"区域管理" summary:"删除区域管理"`
	sysin.AreaInfoDeleteInp
}

type DeleteRes struct{}

// StatusReq 更新区域管理状态
type StatusReq struct {
	g.Meta `path:"/areaInfo/status" method:"post" tags:"区域管理" summary:"更新区域管理状态"`
	sysin.AreaInfoStatusInp
}

type StatusRes struct{}