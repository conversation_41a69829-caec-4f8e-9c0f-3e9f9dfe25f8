// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// areaInfoDao is the data access object for the table hg_area_info.
// You can define custom methods on it to extend its functionality as needed.
type areaInfoDao struct {
	*internal.AreaInfoDao
}

var (
	// AreaInfo is a globally accessible object for table hg_area_info operations.
	AreaInfo = areaInfoDao{internal.NewAreaInfoDao()}
)

// Add your custom methods and functionality below.
