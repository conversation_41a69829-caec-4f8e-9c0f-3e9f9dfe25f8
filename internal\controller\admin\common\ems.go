// Package common

package common

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/api/admin/common"
	"intellos/internal/consts"
	"intellos/internal/library/contexts"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var Ems = new(cEms)

type cEms struct{}

// SendTest 发送测试邮件
func (c *cEms) SendTest(ctx context.Context, req *common.SendTestEmailReq) (res *common.SendTestEmailRes, err error) {
	err = service.SysEmsLog().Send(ctx, &sysin.SendEmsInp{
		Event: consts.EmsTemplateText,
		Email: req.To,
		Content: `
			<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="iso-8859-15">
				<title>这是一封来自intellos的测试邮件</title>
			</head>
			<body>
				这是您通过intellos后台发送的测试邮件。当你收到这封邮件的时候，说明已经联调成功了，恭喜你！
			</body>
			</html>`,
	})
	return
}

// SendBindEms 发送换绑邮件
func (c *cEms) SendBindEms(ctx context.Context, _ *common.SendBindEmsReq) (res *common.SendBindEmsRes, err error) {
	var (
		memberId = contexts.GetUserId(ctx)
		models   *entity.AdminMember
	)

	if memberId <= 0 {
		err = gerror.New("用户身份异常，请重新登录！")
		return
	}

	if err = g.Model("admin_member").Fields("email").Where("id", memberId).Scan(&models); err != nil {
		return
	}

	if models == nil {
		err = gerror.New("用户信息不存在")
		return
	}

	if models.Email == "" {
		err = gerror.New("未绑定邮箱无需发送")
		return
	}

	err = service.SysEmsLog().Send(ctx, &sysin.SendEmsInp{
		Event: consts.EmsTemplateBind,
		Email: models.Email,
	})
	return
}
