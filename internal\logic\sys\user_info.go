// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/enums"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysUserInfo struct{}

func NewSysUserInfo() *sSysUserInfo {
	return &sSysUserInfo{}
}

func init() {
	service.RegisterSysUserInfo(NewSysUserInfo())
}

// Model 人员列表ORM模型
func (s *sSysUserInfo) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.UserInfo.Ctx(ctx), option...)
}

// List 获取人员列表列表
func (s *sSysUserInfo) List(ctx context.Context, in *sysin.UserInfoListInp) (list []*sysin.UserInfoListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.UserInfoListModel{})

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.UserInfo.Columns().Id, in.Id)
	}

	// 查询姓名
	if in.Name != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().Name, in.Name)
	}

	// 查询手机号码
	if in.Mobile != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().Mobile, in.Mobile)
	}

	// 查询身份证
	if in.IdCard != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().IdCard, in.IdCard)
	}

	// 查询工作单位
	if in.WorkUnit != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().WorkUnit, in.WorkUnit)
	}

	// 查询用户身份
	if in.Identity != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().Identity, in.Identity)
	} else {
		mod = mod.WhereNotIn(dao.UserInfo.Columns().Identity, enums.Stranger)
	}

	// 查询状态
	if in.Status != "" {
		mod = mod.WhereLike(dao.UserInfo.Columns().Status, in.Status)
	} else {
		mod = mod.WhereNotIn(dao.UserInfo.Columns().Status, enums.Stranger)
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.UserInfo.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.UserInfo.Columns().UpdatedAt)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取人员列表列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出人员列表
func (s *sSysUserInfo) Export(ctx context.Context, in *sysin.UserInfoListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.UserInfoExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出人员列表-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.UserInfoExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增人员列表
func (s *sSysUserInfo) Edit(ctx context.Context, in *sysin.UserInfoEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		//getUserWorkUnitAndMobile := func(idCard string) (map[string]string, error) {
		//	// 查询是否存在该身份证的用户
		//	record, err := s.Model(ctx).
		//		Where("id_card = ?", in.IdCard).
		//		Where("work_unit != ''").
		//		Where("show_photo != ''").
		//		WhereNotNull("work_unit").
		//		WhereNotNull("show_photo").
		//		One()
		//	if err != nil {
		//		return nil, gerror.Wrap(err, "检查用户是否存在失败")
		//	}
		//
		//	return map[string]string{
		//		"work_unit":  record["work_unit"].String(),
		//		"show_photo": record["show_photo"].String(),
		//	}, nil
		//}

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.UserInfoUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改人员列表失败，请稍后重试！")
			}
			return
		} else {
			todayStart := gtime.Now().StartOfDay()
			todayEnd := gtime.Now().EndOfDay()

			// 查询当天是否存在该身份证的用户
			record, er := s.Model(ctx).
				Where("id_card = ?", in.IdCard).
				Where("created_at BETWEEN ? AND ?", todayStart, todayEnd).
				One()
			if er != nil {
				err = gerror.Wrap(err, "检查用户是否存在失败")
				return
			}

			if !record.IsEmpty() {

				id := record["id"].Int()
				if id == 0 {
					err = gerror.New("未查询到有效的用户ID")
					return
				}

				//result, er := getUserWorkUnitAndMobile(in.IdCard)
				//if er != nil {
				//	err = gerror.New("未查询到有效的用户ID")
				//	return
				//}

				// ✅ 已存在 → 修改
				in.UpdatedBy = contexts.GetUserId(ctx)
				if _, err = s.Model(ctx).
					Fields(sysin.UserInfoHikEventUpdateFields{}).
					Where("id = ?", id).
					Data(in).Update(); err != nil {
					err = gerror.Wrap(err, "更新当天用户失败，请稍后重试")
				}
				return
			} else {
				//result, er := getUserWorkUnitAndMobile(in.IdCard)
				//if er != nil {
				//	err = gerror.New("未查询到有效的用户ID")
				//	return
				//}

				// 新增
				// in.CreatedBy = contexts.GetUserId(ctx)
				in.CreatedBy = 1
				if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
					Fields(sysin.UserInfoInsertFields{}).
					Data(in).OmitEmptyData().Insert(); err != nil {
					err = gerror.Wrap(err, "新增人员列表失败，请稍后重试！")
				}
				return
			}
		}
	})
}

// Delete 删除人员列表
func (s *sSysUserInfo) Delete(ctx context.Context, in *sysin.UserInfoDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除人员列表失败，请稍后重试！")
		return
	}
	return
}

// View 获取人员列表指定信息
func (s *sSysUserInfo) View(ctx context.Context, in *sysin.UserInfoViewInp) (res *sysin.UserInfoViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取人员列表信息，请稍后重试！")
		return
	}
	return
}

// Status 更新人员列表状态
func (s *sSysUserInfo) Status(ctx context.Context, in *sysin.UserInfoStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.UserInfo.Columns().Status:    in.Status,
		dao.UserInfo.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新人员列表状态失败，请稍后重试！")
		return
	}
	return
}
