// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserInfo is the golang structure of table hg_user_info for DAO operations like Where/Data.
type UserInfo struct {
	g.Meta    `orm:"table:hg_user_info, do:true"`
	Id        interface{} // ID
	Name      interface{} // 姓名
	Sex       interface{} // 性别
	Mobile    interface{} // 手机号码
	IdCard    interface{} // 身份证
	WorkUnit  interface{} // 工作单位
	Identity  interface{} // 用户身份
	ShowPhoto interface{} // 展示图片
	SnapPhoto interface{} // 现场抓拍
	FacePhoto interface{} // 人脸识别
	Status    interface{} // 状态
	CreatedBy interface{} // 创建者
	UpdatedBy interface{} // 更新者
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 修改时间
	DeletedAt *gtime.Time // 删除时间
}
