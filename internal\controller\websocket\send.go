// Package websocket

package websocket

import (
	"context"
	"intellos/api/websocket/base"
	"intellos/internal/websocket"
	"intellos/utility/simple"
)

// Send 通过http发送ws消息
var Send = send{}

type send struct{}

// SendToTag 发送标签消息
func (c *send) SendToTag(ctx context.Context, req *base.SendToTagReq) (res *base.SendToTagRes, err error) {
	simple.SafeGo(ctx, func(ctx context.Context) {
		websocket.SendToTag(req.Tag, &websocket.WResponse{
			Event: req.Response.Event,
			Data:  req.Response,
		})
	})
	return
}
