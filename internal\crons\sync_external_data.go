package crons

import (
	"context"
	"intellos/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

// SyncExternalDataCron 同步外部数据定时任务
func SyncExternalDataCron() {
	ctx := context.Background()
	
	// 处理待同步队列
	if err := service.SysDdProjectsExtend().ProcessSyncQueue(ctx); err != nil {
		g.Log().Errorf(ctx, "处理同步队列失败: %v", err)
	}
	
	// 重试失败的同步
	if err := service.SysDdProjectsExtend().RetryFailedSync(ctx); err != nil {
		g.Log().Errorf(ctx, "重试失败同步失败: %v", err)
	}
}
