// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProjectPerson is the golang structure of table hg_project_person for DAO operations like Where/Data.
type ProjectPerson struct {
	g.Meta             `orm:"table:hg_project_person, do:true"`
	Id                 interface{} // ID
	ProjectId          interface{} //
	BidCouncilId       interface{} //
	IdCard             interface{} // 身份证号
	Name               interface{} // 姓名
	Phone              interface{} // 手机号
	RoleId             interface{} // 角色id
	VerifyCode         interface{} //
	WorkOrg            interface{} // 工作单位
	ReportTime         interface{} //
	SignoutAt          interface{} //
	Status             interface{} // 1-已同步，2-在评标室，3-离开
	RoomId             interface{} // 房间id
	Source             interface{} //
	CommitmentPdfStamp interface{} //
	CreatedBy          interface{} // 创建者
	UpdatedBy          interface{} // 更新者
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 修改时间
	DeletedAt          *gtime.Time // 删除时间
}
