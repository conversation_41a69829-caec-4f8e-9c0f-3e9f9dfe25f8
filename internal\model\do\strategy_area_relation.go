// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyAreaRelation is the golang structure of table hg_strategy_area_relation for DAO operations like Where/Data.
type StrategyAreaRelation struct {
	g.Meta     `orm:"table:hg_strategy_area_relation, do:true"`
	Id         interface{} //
	StrategyId interface{} // 策略主表ID
	AreaId     interface{} // 区域ID
	AreaName   interface{} // 区域名称
	IsEnabled  interface{} // 是否启用
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
}
