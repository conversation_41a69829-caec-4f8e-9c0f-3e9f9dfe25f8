package external

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/model/entity"
	"intellos/internal/model/enums"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysHikEventSubscription struct{}

func NewSysHikEventSubscription() *sSysHikEventSubscription {
	return &sSysHikEventSubscription{}
}

func init() {
	service.RegisterSysHikEventSubscription(NewSysHikEventSubscription())
}

// Model 海康事件订阅表ORM模型
func (s *sSysHikEventSubscription) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.HikEventSubscription.Ctx(ctx), option...)
}

// List 获取海康事件订阅表列表
func (s *sSysHikEventSubscription) List(ctx context.Context, in *sysin.HikEventSubscriptionListInp) (list []*sysin.HikEventSubscriptionListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.HikEventSubscriptionListModel{})

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.HikEventSubscription.Columns().Id, in.Id)
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.HikEventSubscription.Columns().Id)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取海康事件订阅表列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出海康事件订阅表
func (s *sSysHikEventSubscription) Export(ctx context.Context, in *sysin.HikEventSubscriptionListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.HikEventSubscriptionExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出海康事件订阅表-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.HikEventSubscriptionExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增海康事件订阅表
func (s *sSysHikEventSubscription) Edit(ctx context.Context, in *sysin.HikEventSubscriptionEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		// 修改
		if in.Id > 0 {
			if _, err = s.Model(ctx).
				Fields(sysin.HikEventSubscriptionUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改海康事件订阅表失败，请稍后重试！")
			}
			return
		}

		// 新增
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.HikEventSubscriptionInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增海康事件订阅表失败，请稍后重试！")
		}

		var userInfo *sysin.UserInfoEditInp
		var baseUser *sysin.BaseUserInfoEditInp
		// 在人员列表中新增
		userInfo = &sysin.UserInfoEditInp{
			entity.UserInfo{
				IdCard:    in.Certificate,
				FacePhoto: in.FacePicUrl,
				WorkUnit:  "",
				SnapPhoto: in.FaceUrl,
				Status:    "1",
			},
		}
		split := strings.Split(in.FaceInfoName, "-")
		if len(split) > 0 {
			userInfo.Name = split[0]
		}
		if in.FaceInfoSex == "male" {
			userInfo.Sex = 1
		} else if in.FaceInfoSex != "" {
			userInfo.Sex = 2
		}

		if userInfo.Identity == "" && in.EventType == int(enums.StrangerEvent) {
			//userInfo.Identity = string(enums.Stranger)
			userInfo.Identity = "陌生人"
		}

		if userInfo.Identity == "" && in.EventType == int(enums.KeyObjectivesEvent) {
			//userInfo.Identity = string(enums.KeyObjectives)
			userInfo.Identity = in.SrcName
		}

		//判断身份证是否为空
		if in.Certificate != "" {
			//查询基础人员列表是否存在
			existBaseUser, _ := service.SysBaseUserInfo().Model(ctx).
				Where(dao.BaseUserInfo.Columns().IdCard, in.Certificate).
				One()

			//基础人员没查到，新建
			if existBaseUser.IsEmpty() {
				baseUser = &sysin.BaseUserInfoEditInp{
					entity.BaseUserInfo{
						IdCard:    in.Certificate,
						FacePhoto: in.FacePicUrl,
						WorkUnit:  "",
						SnapPhoto: in.FaceUrl,
						Status:    1, //新建，默认在场
						Name:      userInfo.Name,
						Sex:       userInfo.Sex,
					},
				}
			} else {
				//查到基本人员，使用基础人员信息更新访客对象
				userInfo = &sysin.UserInfoEditInp{
					entity.UserInfo{
						IdCard:    existBaseUser["id_card"].String(),
						FacePhoto: existBaseUser["face_photo"].String(),
						WorkUnit:  existBaseUser["work_unit"].String(),
						SnapPhoto: existBaseUser["snap_photo"].String(),
						ShowPhoto: existBaseUser["show_photo"].String(),
						Status:    userInfo.Status,
						Name:      existBaseUser["name"].String(),
						Sex:       existBaseUser["sex"].Int(),
						Identity:  userInfo.Identity, // 保持原有身份
					},
				}
			}
		}

		err = service.SysUserInfo().Edit(ctx, userInfo)
		if baseUser != nil && baseUser.IdCard != "" {
			service.SysBaseUserInfo().Edit(ctx, baseUser)
		}
		return
	})
}

// Delete 删除海康事件订阅表
func (s *sSysHikEventSubscription) Delete(ctx context.Context, in *sysin.HikEventSubscriptionDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除海康事件订阅表失败，请稍后重试！")
		return
	}
	return
}

// View 获取海康事件订阅表指定信息
func (s *sSysHikEventSubscription) View(ctx context.Context, in *sysin.HikEventSubscriptionViewInp) (res *sysin.HikEventSubscriptionViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取海康事件订阅表信息，请稍后重试！")
		return
	}
	return
}
