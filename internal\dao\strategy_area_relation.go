// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// strategyAreaRelationDao is the data access object for the table hg_strategy_area_relation.
// You can define custom methods on it to extend its functionality as needed.
type strategyAreaRelationDao struct {
	*internal.StrategyAreaRelationDao
}

var (
	// StrategyAreaRelation is a globally accessible object for table hg_strategy_area_relation operations.
	StrategyAreaRelation = strategyAreaRelationDao{internal.NewStrategyAreaRelationDao()}
)

// Add your custom methods and functionality below.
