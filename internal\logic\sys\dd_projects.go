// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gtime"
	"intellos/internal/consts"
	"intellos/internal/controller/admin/sys"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysDdProjects struct{}

func NewSysDdProjects() *sSysDdProjects {
	return &sSysDdProjects{}
}

func init() {
	service.RegisterSysDdProjects(NewSysDdProjects())
}

// Model 调度系统同步最新评委会接口ORM模型
func (s *sSysDdProjects) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.DdProjects.Ctx(ctx), option...)
}

// List 获取调度系统同步最新评委会接口列表
func (s *sSysDdProjects) List(ctx context.Context, in *sysin.DdProjectsListInp) (list []*sysin.DdProjectsListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.DdProjectsListModel{})

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.DdProjects.Columns().Id, in.Id)
	}

	// 查询房间名称
	if in.RoomName != "" {
		mod = mod.Where(dao.DdProjects.Columns().RoomName, in.RoomName)
	}

	// 查询评委会id
	if in.BidCouncilId != "" {
		mod = mod.Where(dao.DdProjects.Columns().BidCouncilId, in.BidCouncilId)
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.DdProjects.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.DdProjects.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取调度系统同步最新评委会接口列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出调度系统同步最新评委会接口
func (s *sSysDdProjects) Export(ctx context.Context, in *sysin.DdProjectsListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.DdProjectsExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出调度系统同步最新评委会接口-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.DdProjectsExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增调度系统同步最新评委会接口
func (s *sSysDdProjects) Edit(ctx context.Context, in *sysin.DdProjectsEditInp) (err error) {
	var projectId int64

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		// 修改
		if in.Id > 0 {
			projectId = in.Id
			in.UpdatedBy = contexts.GetUserId(ctx)
			// 重置同步状态为待同步
			in.SyncStatus = 0 // consts.SyncStatusPending
			in.SyncRetryCount = 0
			in.SyncErrorMsg = ""

			if _, err = s.Model(ctx).
				Fields(sysin.DdProjectsUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改调度系统同步最新评委会接口失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		// 设置初始同步状态为待同步
		in.SyncStatus = 0 // consts.SyncStatusPending
		in.SyncRetryCount = 0

		result, err := s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.DdProjectsInsertFields{}).
			Data(in).OmitEmptyData().Insert()
		if err != nil {
			err = gerror.Wrap(err, "新增调度系统同步最新评委会接口失败，请稍后重试！")
			return
		}
		// 获取新增的ID
		if lastInsertId, err2 := result.LastInsertId(); err2 == nil {
			projectId = lastInsertId
		}
		return
	})

	if err != nil {
		return err
	}

	// 异步触发数据同步（不阻塞主流程）
	if projectId > 0 {
		go func() {
			syncCtx := context.Background()
			if syncErr := service.SysDdProjects().SyncExternalData(syncCtx, projectId); syncErr != nil {
				g.Log().Errorf(syncCtx, "异步同步项目 %d 数据失败: %v", projectId, syncErr)
			}
		}()
	}

	return nil
}

// Delete 删除调度系统同步最新评委会接口
func (s *sSysDdProjects) Delete(ctx context.Context, in *sysin.DdProjectsDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除调度系统同步最新评委会接口失败，请稍后重试！")
		return
	}
	return
}

// View 获取调度系统同步最新评委会接口指定信息
func (s *sSysDdProjects) View(ctx context.Context, in *sysin.DdProjectsViewInp) (res *sysin.DdProjectsViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取调度系统同步最新评委会接口信息，请稍后重试！")
		return
	}
	return
}

// SyncExternalData 同步外部系统数据
func (s *sSysDdProjects) SyncExternalData(ctx context.Context, projectId int64) (err error) {
	// 1. 获取项目信息
	var project *entity.DdProjects
	if err = dao.DdProjects.Ctx(ctx).WherePri(projectId).Scan(&project); err != nil {
		return gerror.Wrap(err, "获取项目信息失败")
	}
	if project == nil {
		return gerror.New("项目不存在")
	}

	// 2. 更新同步状态为处理中
	if err = s.updateSyncStatus(ctx, projectId, consts.SyncStatusProcessing, "", 0); err != nil {
		return gerror.Wrap(err, "更新同步状态失败")
	}

	// 3. 调用外部系统获取数据
	extraData, err := s.fetchExternalData(ctx, project)
	if err != nil {
		// 更新失败状态
		s.updateSyncStatus(ctx, projectId, consts.SyncStatusFailed, err.Error(), project.SyncRetryCount+1)
		return gerror.Wrap(err, "获取外部数据失败")
	}

	// 4. 更新数据库
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		//将数据保存至projectperson
		var persons []*sysin.ProjectPersonEditInp
		for _, person := range extraData.Data {
			persons = append(persons, entity.ProjectPerson{
				ProjectId:    projectId,
				BidCouncilId: project.BidCouncilId,
				IdCard:       person.IdCard,
				Name:         person.Name,
				Phone:        person.Phone,
				RoleId:       person.RoleId,
				VerifyCode:   person.VerifyCode,
				WorkOrg:      person.WorkOrg,
				ReportTime:   person.ReportTime,
				SignoutAt:    person.SignoutAt, // 系统用户
			})
		}
		if len(persons) > 0 {
			service.SysProjectPerson().BatchSave() // 先删除旧数据
		}
		if _, err = sys.ProjectPerson.BatchInsert(persons, 100); err != nil {
			return gerror.Wrap(err, "批量插入项目人员数据失败")
		}

		extraDataJson, _ := json.Marshal(extraData)
		updateData := g.Map{
			"extra_data":       string(extraDataJson),
			"sync_status":      consts.SyncStatusSuccess,
			"sync_error_msg":   "",
			"sync_retry_count": 0,
			"last_sync_at":     gtime.Now(),
		}

		if _, err = dao.DdProjects.Ctx(ctx).WherePri(projectId).Data(updateData).Update(); err != nil {
			return gerror.Wrap(err, "更新项目数据失败")
		}

		g.Log().Infof(ctx, "项目 %d 同步外部数据成功", projectId)
		return nil
	})
}

// fetchExternalData 获取外部系统数据
func (s *sSysDdProjects) fetchExternalData(ctx context.Context, project *entity.DdProjects) (data *model.DDProjectPersonListResponse, err error) {
	// 构建请求参数
	requestData := &model.DDProjectPersonListRequestParam{
		ProjectId:  project.BidCouncilId,
		Decryption: "1",
	}

	// 创建HTTP客户端
	client := gclient.New()
	client.SetTimeout(10 * time.Second) // 设置超时时间
	client.SetHeader("Content-Type", "application/json;charset=utf-8")

	// 调用外部系统API（这里需要替换为实际的外部系统地址）
	externalApiUrl := "http://172.16.175.161/api/sched/v1/project/person/list"

	response, err := client.Post(ctx, externalApiUrl, requestData)
	if err != nil {
		return nil, gerror.Wrapf(err, "调用外部系统API失败: %s", externalApiUrl)
	}
	defer response.Close()

	// 解析响应
	var apiResponse model.DDProjectPersonListResponse
	responseBody := response.ReadAll()
	g.Log().Infof(ctx, "外部系统响应: %s", responseBody)
	if err = json.Unmarshal((responseBody), &apiResponse); err != nil {
		return nil, gerror.Wrap(err, "解析外部系统响应失败")
	}

	if apiResponse.Errmsg != "success" || apiResponse.Errcode != 0 {
		return nil, gerror.Newf("外部系统返回错误: %s", apiResponse.Errmsg)
	}

	return &apiResponse, nil
}

// updateSyncStatus 更新同步状态
func (s *sSysDdProjects) updateSyncStatus(ctx context.Context, projectId int64, status int, errorMsg string, retryCount int) error {
	updateData := g.Map{
		"sync_status":      status,
		"sync_error_msg":   errorMsg,
		"sync_retry_count": retryCount,
		"last_sync_at":     gtime.Now(),
	}

	_, err := dao.DdProjects.Ctx(ctx).WherePri(projectId).Data(updateData).Update()
	return err
}

// ProcessSyncQueue 处理同步队列（处理待同步的数据）
func (s *sSysDdProjects) ProcessSyncQueue(ctx context.Context) (err error) {
	// 查询待同步的项目
	var projects []*entity.DdProjects
	if err = dao.DdProjects.Ctx(ctx).
		Where("sync_status", consts.SyncStatusPending).
		Limit(10). // 每次处理10条
		Scan(&projects); err != nil {
		return gerror.Wrap(err, "查询待同步项目失败")
	}

	if len(projects) == 0 {
		g.Log().Debug(ctx, "没有待同步的项目")
		return nil
	}

	// 异步处理每个项目
	for _, project := range projects {
		go func(projectId int64) {
			// 创建新的上下文，避免超时影响
			syncCtx := context.Background()
			if err := s.SyncExternalData(syncCtx, projectId); err != nil {
				g.Log().Errorf(syncCtx, "同步项目 %d 失败: %v", projectId, err)
			}
		}(project.Id)
	}

	g.Log().Infof(ctx, "开始处理 %d 个待同步项目", len(projects))
	return nil
}

// RetryFailedSync 重试失败的同步
func (s *sSysDdProjects) RetryFailedSync(ctx context.Context) (err error) {
	// 查询同步失败且重试次数未超限的项目
	var projects []*entity.DdProjects
	if err = dao.DdProjects.Ctx(ctx).
		Where("sync_status", consts.SyncStatusFailed).
		Where("sync_retry_count < ?", consts.MaxSyncRetryCount).
		Limit(5). // 每次重试5条
		Scan(&projects); err != nil {
		return gerror.Wrap(err, "查询失败同步项目失败")
	}

	if len(projects) == 0 {
		g.Log().Debug(ctx, "没有需要重试的项目")
		return nil
	}

	// 异步重试每个项目
	for _, project := range projects {
		go func(projectId int64) {
			syncCtx := context.Background()
			if err := s.SyncExternalData(syncCtx, projectId); err != nil {
				g.Log().Errorf(syncCtx, "重试同步项目 %d 失败: %v", projectId, err)
			}
		}(project.Id)
	}

	g.Log().Infof(ctx, "开始重试 %d 个失败同步项目", len(projects))
	return nil
}
