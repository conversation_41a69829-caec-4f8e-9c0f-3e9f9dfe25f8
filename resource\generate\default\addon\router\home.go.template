// Package router

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"intellos/addons/@{.name}/controller/home"
	"intellos/addons/@{.name}/global"
	"intellos/internal/consts"
	"intellos/internal/library/addons"
)

// Home 前台页面路由
func Home(ctx context.Context, group *ghttp.RouterGroup) {
	prefix := addons.RouterPrefix(ctx, consts.AppHome, global.GetSkeleton().Name)
	group.Group(prefix, func(group *ghttp.RouterGroup) {
		group.Bind(
			home.Index,
		)
	})
}
