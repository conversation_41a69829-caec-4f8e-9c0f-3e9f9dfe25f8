package resp

type DDProjectPersonListRequestParam struct {
	ProjectId  string `json:"project_id"`
	Decryption string `json:"decryption"`
}

type DDProjectPersonListResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Data    []struct {
		Id                 int32  `json:"id"`
		IdCard             string `json:"id_card"`
		Name               string `json:"name"`
		Phone              string `json:"phone"`
		RoleId             int    `json:"role_id"`
		Status             int    `json:"status"`
		VerifyCode         string `json:"verify_code"`
		WorkOrg            string `json:"work_org"`
		ReportTime         string `json:"report_time"`
		SignoutAt          string `json:"signout_at"`
		RoomId             string `json:"room_id"`
		Source             string `json:"source"`
		CommitmentPdfStamp string `json:"commitmentPdfStamp"`
	} `json:"data"`
}
