// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyAreaRelation is the golang structure for table strategy_area_relation.
type StrategyAreaRelation struct {
	Id         int64       `json:"id"         orm:"id"          description:""`
	StrategyId int64       `json:"strategyId" orm:"strategy_id" description:"策略主表ID"`
	AreaId     int64       `json:"areaId"     orm:"area_id"     description:"区域ID"`
	AreaName   string      `json:"areaName"   orm:"area_name"   description:"区域名称"`
	IsEnabled  int         `json:"isEnabled"  orm:"is_enabled"  description:"是否启用"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`
}
