// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"
	"intellos/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProjectPersonUpdateFields 修改调度系统项目人员列表字段过滤
type ProjectPersonUpdateFields struct {
	IdCard             string `json:"idCard"             dc:"身份证号"`
	Name               int    `json:"name"               dc:"姓名"`
	Phone              string `json:"phone"              dc:"手机号"`
	RoleId             int    `json:"roleId"             dc:"角色id"`
	VerifyCode         string `json:"verifyCode"         dc:"verify_code"`
	WorkOrg            string `json:"workOrg"            dc:"工作单位"`
	ReportTime         int    `json:"reportTime"         dc:"report_time"`
	SignoutAt          int    `json:"signoutAt"          dc:"signout_at"`
	Status             string `json:"status"             dc:"1-在评标室，2-离开"`
	RoomId             int    `json:"roomId"             dc:"房间id"`
	Source             string `json:"source"             dc:"source"`
	CommitmentPdfStamp string `json:"commitmentPdfStamp" dc:"commitmentPdfStamp"`
	UpdatedBy          int64  `json:"updatedBy"          dc:"更新者"`
}

// ProjectPersonInsertFields 新增调度系统项目人员列表字段过滤
type ProjectPersonInsertFields struct {
	IdCard             string `json:"idCard"             dc:"身份证号"`
	Name               int    `json:"name"               dc:"姓名"`
	Phone              string `json:"phone"              dc:"手机号"`
	RoleId             int    `json:"roleId"             dc:"角色id"`
	VerifyCode         string `json:"verifyCode"         dc:"verify_code"`
	WorkOrg            string `json:"workOrg"            dc:"工作单位"`
	ReportTime         int    `json:"reportTime"         dc:"report_time"`
	SignoutAt          int    `json:"signoutAt"          dc:"signout_at"`
	Status             string `json:"status"             dc:"1-在评标室，2-离开"`
	RoomId             int    `json:"roomId"             dc:"房间id"`
	Source             string `json:"source"             dc:"source"`
	CommitmentPdfStamp string `json:"commitmentPdfStamp" dc:"commitmentPdfStamp"`
	CreatedBy          int64  `json:"createdBy"          dc:"创建者"`
}

// ProjectPersonEditInp 修改/新增调度系统项目人员列表
type ProjectPersonEditInp struct {
	entity.ProjectPerson
}

func (in *ProjectPersonEditInp) Filter(ctx context.Context) (err error) {
	// 验证身份证号
	if err := g.Validator().Rules("resident-id").Data(in.IdCard).Messages("身份证号不是身份证号码").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type ProjectPersonEditModel struct{}

// ProjectPersonDeleteInp 删除调度系统项目人员列表
type ProjectPersonDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ProjectPersonDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type ProjectPersonDeleteModel struct{}

// ProjectPersonViewInp 获取指定调度系统项目人员列表信息
type ProjectPersonViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ProjectPersonViewInp) Filter(ctx context.Context) (err error) {
	return
}

type ProjectPersonViewModel struct {
	entity.ProjectPerson
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// ProjectPersonListInp 获取调度系统项目人员列表列表
type ProjectPersonListInp struct {
	form.PageReq
	Id        int64         `json:"id"        dc:"ID"`
	CreatedAt []*gtime.Time `json:"createdAt" dc:"创建时间"`
}

func (in *ProjectPersonListInp) Filter(ctx context.Context) (err error) {
	return
}

type ProjectPersonListModel struct {
	Id                 int64             `json:"id"                 dc:"ID"`
	IdCard             string            `json:"idCard"             dc:"身份证号"`
	Name               int               `json:"name"               dc:"姓名"`
	Phone              string            `json:"phone"              dc:"手机号"`
	RoleId             int               `json:"roleId"             dc:"角色id"`
	ReportTime         int               `json:"reportTime"         dc:"report_time"`
	SignoutAt          int               `json:"signoutAt"          dc:"signout_at"`
	Status             string            `json:"status"             dc:"1-在评标室，2-离开"`
	RoomId             int               `json:"roomId"             dc:"房间id"`
	Source             string            `json:"source"             dc:"source"`
	CommitmentPdfStamp string            `json:"commitmentPdfStamp" dc:"commitmentPdfStamp"`
	CreatedBy          int64             `json:"createdBy"          dc:"创建者"`
	CreatedBySumma     *hook.MemberSumma `json:"createdBySumma"     dc:"创建者摘要信息"`
	UpdatedBy          int64             `json:"updatedBy"          dc:"更新者"`
	UpdatedBySumma     *hook.MemberSumma `json:"updatedBySumma"     dc:"更新者摘要信息"`
	CreatedAt          *gtime.Time       `json:"createdAt"          dc:"创建时间"`
	UpdatedAt          *gtime.Time       `json:"updatedAt"          dc:"修改时间"`
}

// ProjectPersonExportModel 导出调度系统项目人员列表
type ProjectPersonExportModel struct {
	Id                 int64       `json:"id"                 dc:"ID"`
	IdCard             string      `json:"idCard"             dc:"身份证号"`
	Name               int         `json:"name"               dc:"姓名"`
	Phone              string      `json:"phone"              dc:"手机号"`
	RoleId             int         `json:"roleId"             dc:"角色id"`
	ReportTime         int         `json:"reportTime"         dc:"report_time"`
	SignoutAt          int         `json:"signoutAt"          dc:"signout_at"`
	Status             string      `json:"status"             dc:"1-在评标室，2-离开"`
	RoomId             int         `json:"roomId"             dc:"房间id"`
	Source             string      `json:"source"             dc:"source"`
	CommitmentPdfStamp string      `json:"commitmentPdfStamp" dc:"commitmentPdfStamp"`
	CreatedBy          int64       `json:"createdBy"          dc:"创建者"`
	UpdatedBy          int64       `json:"updatedBy"          dc:"更新者"`
	CreatedAt          *gtime.Time `json:"createdAt"          dc:"创建时间"`
	UpdatedAt          *gtime.Time `json:"updatedAt"          dc:"修改时间"`
}

// ProjectPersonStatusInp 更新调度系统项目人员列表状态
type ProjectPersonStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *ProjectPersonStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type ProjectPersonStatusModel struct{}