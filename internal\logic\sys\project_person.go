// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysProjectPerson struct{}

func NewSysProjectPerson() *sSysProjectPerson {
	return &sSysProjectPerson{}
}

func init() {
	service.RegisterSysProjectPerson(NewSysProjectPerson())
}

// Model 调度系统项目人员列表ORM模型
func (s *sSysProjectPerson) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.ProjectPerson.Ctx(ctx), option...)
}

// List 获取调度系统项目人员列表列表
func (s *sSysProjectPerson) List(ctx context.Context, in *sysin.ProjectPersonListInp) (list []*sysin.ProjectPersonListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.ProjectPersonListModel{})

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.ProjectPerson.Columns().Id, in.Id)
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.ProjectPerson.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.ProjectPerson.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取调度系统项目人员列表列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出调度系统项目人员列表
func (s *sSysProjectPerson) Export(ctx context.Context, in *sysin.ProjectPersonListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.ProjectPersonExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出调度系统项目人员列表-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.ProjectPersonExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增调度系统项目人员列表
func (s *sSysProjectPerson) Edit(ctx context.Context, in *sysin.ProjectPersonEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.ProjectPersonUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改调度系统项目人员列表失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.ProjectPersonInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增调度系统项目人员列表失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除调度系统项目人员列表
func (s *sSysProjectPerson) Delete(ctx context.Context, in *sysin.ProjectPersonDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除调度系统项目人员列表失败，请稍后重试！")
		return
	}
	return
}

// View 获取调度系统项目人员列表指定信息
func (s *sSysProjectPerson) View(ctx context.Context, in *sysin.ProjectPersonViewInp) (res *sysin.ProjectPersonViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取调度系统项目人员列表信息，请稍后重试！")
		return
	}
	return
}

// Status 更新调度系统项目人员列表状态
func (s *sSysProjectPerson) Status(ctx context.Context, in *sysin.ProjectPersonStatusInp) (err error) {
	if _, err = s.Model(ctx).WherePri(in.Id).Data(g.Map{
		dao.ProjectPerson.Columns().Status:    in.Status,
		dao.ProjectPerson.Columns().UpdatedBy: contexts.GetUserId(ctx),
	}).Update(); err != nil {
		err = gerror.Wrap(err, "更新调度系统项目人员列表状态失败，请稍后重试！")
		return
	}
	return
}

// BatchSave 批量保存调度系统项目人员列表
func (s *sSysProjectPerson) BatchSave(ctx context.Context, in []*sysin.ProjectPersonEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		// 获取当前用户ID
		userId := contexts.GetUserId(ctx)

		// 批量数据处理
		for _, item := range in {
			if item.Id > 0 {
				// 更新已存在记录
				item.UpdatedBy = userId
				if _, err = s.Model(ctx).
					Fields(sysin.ProjectPersonUpdateFields{}).
					WherePri(item.Id).
					Data(item).
					Update(); err != nil {
					return gerror.Wrap(err, "批量更新调度系统项目人员列表失败")
				}
			} else {
				// 新增记录
				item.CreatedBy = userId
				if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
					Fields(sysin.ProjectPersonInsertFields{}).
					Data(item).
					OmitEmptyData().
					Insert(); err != nil {
					return gerror.Wrap(err, "批量新增调度系统项目人员列表失败")
				}
			}
		}
		return nil
	})
}
