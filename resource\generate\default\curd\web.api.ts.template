import { http, jumpExport } from '@/utils/http/axios';

// 获取@{.tableComment}列表
export function List(params) {
  return http.request({
    url: '/@{.apiPrefix}/list',
    method: 'get',
    params,
  });
}
@{ if eq .options.Step.HasDel true }
// 删除/批量删除@{.tableComment}
export function Delete(params) {
  return http.request({
    url: '/@{.apiPrefix}/delete',
    method: 'POST',
    params,
  });
}
@{end}
@{ if eq .options.Step.HasEdit true }
// 添加/编辑@{.tableComment}
export function Edit(params) {
  return http.request({
    url: '/@{.apiPrefix}/edit',
    method: 'POST',
    params,
  });
}
@{end}
@{ if eq .options.Step.HasStatus true }
// 修改@{.tableComment}状态
export function Status(params) {
  return http.request({
    url: '/@{.apiPrefix}/status',
    method: 'POST',
    params,
  });
}
@{end}
@{ if eq .options.Step.HasSwitch true }
// 操作@{.tableComment}开关
export function Switch(params) {
  return http.request({
    url: '/@{.apiPrefix}/switch',
    method: 'POST',
    params,
  });
}
@{end}
@{ if or (eq .options.Step.HasView true) (eq .options.Step.HasEdit true) }
// 获取@{.tableComment}指定详情
export function View(params) {
  return http.request({
    url: '/@{.apiPrefix}/view',
    method: 'GET',
    params,
  });
}
@{end}
@{ if eq .options.Step.HasMaxSort true }
// 获取@{.tableComment}最大排序
export function MaxSort() {
  return http.request({
    url: '/@{.apiPrefix}/maxSort',
    method: 'GET',
  });
}
@{end}
@{ if eq .options.Step.HasExport true }
// 导出@{.tableComment}
export function Export(params) {
  jumpExport('/@{.apiPrefix}/export', params);
}
@{end}
@{ if eq .options.Step.IsTreeTable true }
// 获取@{.tableComment}关系树选项
export function TreeOption() {
  return http.request({
    url: '/@{.apiPrefix}/treeOption',
    method: 'GET',
  });
}
@{end}