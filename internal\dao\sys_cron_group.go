// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysCronGroupDao is the data access object for the table hg_sys_cron_group.
// You can define custom methods on it to extend its functionality as needed.
type sysCronGroupDao struct {
	*internal.SysCronGroupDao
}

var (
	// SysCronGroup is a globally accessible object for table hg_sys_cron_group operations.
	SysCronGroup = sysCronGroupDao{internal.NewSysCronGroupDao()}
)

// Add your custom methods and functionality below.
