// Package router

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"intellos/internal/consts"
	controller "intellos/internal/controller/websocket"
	"intellos/internal/controller/websocket/handler/admin"
	"intellos/internal/controller/websocket/handler/common"
	"intellos/internal/service"
	"intellos/internal/websocket"
	"intellos/utility/simple"
)

// WebSocket ws路由配置
func WebSocket(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group(simple.RouterPrefix(ctx, consts.AppWebSocket), func(group *ghttp.RouterGroup) {
		group.Middleware()
		group.Bind(
			controller.Send, // 通过http发送ws消息。方便测试没有放权限中间件，实际使用时请自行调整
		)

		// ws连接中间件
		group.Middleware(service.Middleware().WebSocketAuth)

		// ws
		group.GET("/", websocket.WsPage)
	})

	// 启动websocket监听
	websocket.Start()

	// 注册消息路由
	websocket.RegisterMsg(websocket.EventHandlers{
		"ping":                  common.Site.Ping,      // 心跳
		"join":                  common.Site.Join,      // 加入组
		"quit":                  common.Site.Quit,      // 退出组
		"admin/monitor/trends":  admin.Monitor.Trends,  // 后台监控，动态数据
		"admin/monitor/runInfo": admin.Monitor.RunInfo, // 后台监控，运行信息
	})
}
