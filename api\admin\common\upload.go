// Package common

package common

import (
	"github.com/gogf/gf/v2/frame/g"
	"intellos/internal/model/input/sysin"
)

// UploadFileReq 上传文件
type UploadFileReq struct {
	g.Meta `path:"/upload/file" tags:"附件" method:"post" summary:"上传附件"`
}

type UploadFileRes *sysin.AttachmentListModel

// CheckMultipartReq 检查文件分片
type CheckMultipartReq struct {
	g.Meta `path:"/upload/checkMultipart" tags:"附件" method:"post" summary:"检查文件分片"`
	sysin.CheckMultipartInp
}

type CheckMultipartRes struct {
	*sysin.CheckMultipartModel
}

// UploadPartReq 分片上传
type UploadPartReq struct {
	g.Meta `path:"/upload/uploadPart" tags:"附件" method:"post" summary:"分片上传"`
	sysin.UploadPartInp
}

type UploadPartRes struct {
	*sysin.UploadPartModel
}
