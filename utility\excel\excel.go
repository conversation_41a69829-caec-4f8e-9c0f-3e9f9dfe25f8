// Package excel

package excel

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/xuri/excelize/v2"
	"intellos/internal/library/contexts"
	"intellos/internal/model"
	"intellos/utility/convert"
	"io"
	"net/url"
	"reflect"
	"slices"
	"strconv"
	"strings"
	"time"
	"unicode"
)

var (
	// 单元格表头
	char = []string{"", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"}
	// 默认行样式
	defaultRowStyle = `{"font":{"color":"#666666","size":13,"family":"arial"},"alignment":{"vertical":"center","horizontal":"center"}}`
)

// ExportByStructs 导出切片结构体到excel表格
func ExportByStructs(ctx context.Context, tags []string, list interface{}, fileName string, sheetName string) (err error) {
	f := excelize.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	_ = f.SetRowHeight("Sheet1", 1, 30)

	rowStyleID, _ := f.NewStyle(defaultRowStyle)
	if err != nil {
		return
	}
	_ = f.SetSheetRow(sheetName, "A1", &tags)

	var (
		length    = len(tags)
		headStyle = letter(length)
		lastRow   string
		widthRow  string
	)

	for k, v := range headStyle {
		if k == length-1 {
			lastRow = fmt.Sprintf("%s1", v)
			widthRow = v
		}
	}

	if err = f.SetColWidth(sheetName, "A", widthRow, 30); err != nil {
		return err
	}

	var rowNum = 1
	for _, v := range gconv.Interfaces(list) {
		t := reflect.TypeOf(v)
		value := reflect.ValueOf(v)
		row := make([]interface{}, 0)
		for l := 0; l < t.NumField(); l++ {
			val := value.Field(l).Interface()
			row = append(row, val)
		}
		rowNum++
		if err = f.SetSheetRow(sheetName, "A"+gconv.String(rowNum), &row); err != nil {
			return
		}
		if err = f.SetCellStyle(sheetName, fmt.Sprintf("A%d", rowNum), lastRow, rowStyleID); err != nil {
			return
		}
	}

	r := ghttp.RequestFromCtx(ctx)
	if r == nil {
		err = gerror.New("ctx not http request")
		return
	}

	writer := r.Response.Writer
	writer.Header().Set("Content-Type", "application/octet-stream")
	writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape(fileName)))
	writer.Header().Set("Content-Transfer-Encoding", "binary")
	writer.Header().Set("Access-Control-Expose-Headers", "Content-Disposition")

	if err = f.Write(writer); err != nil {
		return
	}

	// 加入到上下文
	contexts.SetResponse(ctx, &model.Response{
		Code:      gcode.CodeOK.Code(),
		Message:   "export successfully!",
		Timestamp: time.Now().Unix(),
		TraceID:   gctx.CtxId(ctx),
	})
	return
}

// ImportByStructs 导入excel表格到切片结构体
func ImportByStructs[T any](ctx context.Context, r io.Reader) (result []T, err error) {
	excel, err := excelize.OpenReader(r)
	if err != nil {
		return nil, fmt.Errorf("无法打开 Excel 文件: %w", err)
	}
	defer excel.Close()

	sheetList := excel.GetSheetList()
	for _, sheet := range sheetList {
		rows, err := excel.GetRows(sheet)
		if err != nil {
			return result, fmt.Errorf("读取 sheet[%s] 数据失败: %w", sheet, err)
		}
		if len(rows) < 2 {
			g.Log().Infof(ctx, "ImportByStructs: excel.sheet[%s] 中只有一行数据, 不执行导入操作", sheet)
			continue
		}

		// 获取表头
		headers := rows[0]

		// 遍历数据行
		for i := 1; i < len(rows); i++ {
			row := rows[i]
			if isRowEmpty(row) {
				continue
			}

			// 创建结构体实例
			var item T
			itemVal := reflect.ValueOf(&item).Elem()
			itemType := itemVal.Type()

			tags, err := convert.GetEntityDescTags(item)
			if err != nil {
				g.Log().Error(ctx, "ImportByStructs: 实体类tag转换错误, ", err.Error())
			}
			for j, header := range headers {
				if j >= len(row) {
					break
				}
				cellValue := row[j]

				//field, ok := itemType.FieldByNameFunc(func(fieldName string) bool {
				//	if len(tags) != 0 {
				//		structField, ok := itemType.FieldByName(fieldName)
				//		if ok {
				//			convert.GetEntitySomebodyDescTag(structField)
				//		}
				//	}
				//	return fieldName == header
				//})
				//if !ok {
				//	continue
				//}
				field, ok := findMatchingField(itemType, tags, header)
				if !ok {
					continue
				}

				fieldVal := itemVal.FieldByName(field.Name)
				if !fieldVal.CanSet() {
					g.Log().Error(ctx, "ImportByStructs: 获取字段失败, ", field.Name)
					continue
				}

				setFieldVal(ctx, fieldVal, cellValue)
			}

			result = append(result, item)
		}
	}

	return
}

// findMatchingField 根据表头名称查找对应的结构体字段，优先匹配 json tag
func findMatchingField(typ reflect.Type, tags []string, header string) (reflect.StructField, bool) {
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		tag := convert.GetEntitySomebodyDescTag(field)
		if slices.Contains(tags, tag) {
			return field, true
		} else if tag == header {
			return field, true
		}
	}
	return reflect.StructField{}, false
}

// setFieldVal 设置字段值
func setFieldVal(ctx context.Context, fieldVal reflect.Value, cellValue string) {
	switch fieldVal.Kind() {
	case reflect.String:
		fieldVal.SetString(cellValue)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if num, err := strconv.ParseInt(cellValue, 10, 64); err == nil {
			fieldVal.SetInt(num)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if num, err := strconv.ParseUint(cellValue, 10, 64); err == nil {
			fieldVal.SetUint(num)
		}
	case reflect.Float32, reflect.Float64:
		if num, err := strconv.ParseFloat(cellValue, 64); err == nil {
			fieldVal.SetFloat(num)
		}
	case reflect.Bool:
		b, _ := strconv.ParseBool(strings.ToLower(cellValue))
		fieldVal.SetBool(b)
	default:
		g.Log().Warning(ctx, "ImportByStructs: 不支持的数据类型，", fieldVal.Kind())
		// 其他类型可扩展
	}
}

// isRowEmpty 检查某一行是否全是空字符串
func isRowEmpty(row []string) bool {
	for _, cell := range row {
		if cell != "" {
			return false
		}
	}
	return true
}

// letter 生成完整的表头
func letter(length int) []string {
	var str []string
	for i := 1; i <= length; i++ {
		str = append(str, numToChars(i))
	}
	return str
}

// numToChars 将数字转换为具体的表格表头名称
func numToChars(num int) string {
	var cols string
	v := num
	for v > 0 {
		k := v % 26
		if k == 0 {
			k = 26
		}
		v = (v - k) / 26
		cols = char[k] + cols
	}
	return cols
}

// NextLetter 传入一个字母，获取下一个字母
func NextLetter(input string) string {
	if len(input) == 0 {
		return ""
	}
	upperInput := unicode.ToUpper(rune(input[0]))
	if upperInput >= 'A' && upperInput < 'Z' {
		return string(upperInput + 1)
	}
	return "A"
}

type ExcelSheetData struct {
	Sheet string     `json:"sheet"`
	Rows  [][]string `json:"rows"`
}

type ExcelStruct struct {
	Sheets []*ExcelSheetData `json:"sheets"`
}
