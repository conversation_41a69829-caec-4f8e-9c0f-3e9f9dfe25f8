// Package strategycondition
// @AutoGenerate Version 2.16.10
package strategycondition

import (
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"

	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 查询策略条件列表
type ListReq struct {
	g.Meta `path:"/strategyCondition/list" method:"get" tags:"策略条件" summary:"获取策略条件列表"`
	sysin.StrategyConditionListInp
}

type ListRes struct {
	form.PageRes
	List []*sysin.StrategyConditionListModel `json:"list"   dc:"数据列表"`
}

// ExportReq 导出策略条件列表
type ExportReq struct {
	g.Meta `path:"/strategyCondition/export" method:"get" tags:"策略条件" summary:"导出策略条件列表"`
	sysin.StrategyConditionListInp
}

type ExportRes struct{}

// ViewReq 获取策略条件指定信息
type ViewReq struct {
	g.Meta `path:"/strategyCondition/view" method:"get" tags:"策略条件" summary:"获取策略条件指定信息"`
	sysin.StrategyConditionViewInp
}

type ViewRes struct {
	*sysin.StrategyConditionViewModel
}

// EditReq 修改/新增策略条件
type EditReq struct {
	g.Meta `path:"/strategyCondition/edit" method:"post" tags:"策略条件" summary:"修改/新增策略条件"`
	sysin.StrategyConditionEditInp
}

type EditRes struct{}

// DeleteReq 删除策略条件
type DeleteReq struct {
	g.Meta `path:"/strategyCondition/delete" method:"post" tags:"策略条件" summary:"删除策略条件"`
	sysin.StrategyConditionDeleteInp
}

type DeleteRes struct{}
