// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// sysSmsLogDao is the data access object for the table hg_sys_sms_log.
// You can define custom methods on it to extend its functionality as needed.
type sysSmsLogDao struct {
	*internal.SysSmsLogDao
}

var (
	// SysSmsLog is a globally accessible object for table hg_sys_sms_log operations.
	SysSmsLog = sysSmsLogDao{internal.NewSysSmsLogDao()}
)

// Add your custom methods and functionality below.
