// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyAreaRelationUpdateFields 修改区域策略关联表字段过滤
type StrategyAreaRelationUpdateFields struct {
	StrategyId int64  `json:"strategyId" dc:"策略主表ID"`
	AreaId     int64  `json:"areaId"     dc:"区域ID"`
	AreaName   string `json:"areaName"   dc:"区域名称"`
	IsEnabled  int    `json:"isEnabled"  dc:"是否启用"`
}

// StrategyAreaRelationInsertFields 新增区域策略关联表字段过滤
type StrategyAreaRelationInsertFields struct {
	StrategyId int64  `json:"strategyId" dc:"策略主表ID"`
	AreaId     int64  `json:"areaId"     dc:"区域ID"`
	AreaName   string `json:"areaName"   dc:"区域名称"`
	IsEnabled  int    `json:"isEnabled"  dc:"是否启用"`
}

// StrategyAreaRelationEditInp 修改/新增区域策略关联表
type StrategyAreaRelationEditInp struct {
	entity.StrategyAreaRelation
}

func (in *StrategyAreaRelationEditInp) Filter(ctx context.Context) (err error) {
	// 验证策略主表ID
	if err := g.Validator().Rules("required").Data(in.StrategyId).Messages("策略主表ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证区域ID
	if err := g.Validator().Rules("required").Data(in.AreaId).Messages("区域ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证区域名称
	if err := g.Validator().Rules("required").Data(in.AreaName).Messages("区域名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type StrategyAreaRelationEditModel struct{}

// StrategyAreaRelationDeleteInp 删除区域策略关联表
type StrategyAreaRelationDeleteInp struct {
	Id interface{} `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyAreaRelationDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyAreaRelationDeleteModel struct{}

// StrategyAreaRelationViewInp 获取指定区域策略关联表信息
type StrategyAreaRelationViewInp struct {
	Id int64 `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyAreaRelationViewInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyAreaRelationViewModel struct {
	entity.StrategyAreaRelation
}

// StrategyAreaRelationListInp 获取区域策略关联表列表
type StrategyAreaRelationListInp struct {
	form.PageReq
	Id        int64         `json:"id"        dc:"id"`
	CreatedAt []*gtime.Time `json:"createdAt" dc:"created_at"`
}

func (in *StrategyAreaRelationListInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyAreaRelationListModel struct {
	Id         int64       `json:"id"         dc:"id"`
	StrategyId int64       `json:"strategyId" dc:"策略主表ID"`
	AreaId     int64       `json:"areaId"     dc:"区域ID"`
	AreaName   string      `json:"areaName"   dc:"区域名称"`
	IsEnabled  int         `json:"isEnabled"  dc:"是否启用"`
	CreatedAt  *gtime.Time `json:"createdAt"  dc:"created_at"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  dc:"updated_at"`
}

// StrategyAreaRelationExportModel 导出区域策略关联表
type StrategyAreaRelationExportModel struct {
	Id         int64       `json:"id"         dc:"id"`
	StrategyId int64       `json:"strategyId" dc:"策略主表ID"`
	AreaId     int64       `json:"areaId"     dc:"区域ID"`
	AreaName   string      `json:"areaName"   dc:"区域名称"`
	IsEnabled  int         `json:"isEnabled"  dc:"是否启用"`
	CreatedAt  *gtime.Time `json:"createdAt"  dc:"created_at"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  dc:"updated_at"`
}