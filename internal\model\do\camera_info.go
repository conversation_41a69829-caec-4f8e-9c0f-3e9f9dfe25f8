// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CameraInfo is the golang structure of table hg_camera_info for DAO operations like Where/Data.
type CameraInfo struct {
	g.Meta     `orm:"table:hg_camera_info, do:true"`
	Id         interface{} //
	AreaId     interface{} // 区域id
	CameraCode interface{} // 摄像头编码
	CameraName interface{} // 摄像头名称
	CameraType interface{} // 摄像头类型:hik,dahua,uniview
	DdCode     interface{} // 调度系统code
	HikCode    interface{} // 海康系统内唯一物理id
	Location   interface{} // 安装位置
	Direction  interface{} // 朝向
	Status     interface{} // 状态:1启用,2禁用
	Remark     interface{} // 备注
	CreatedBy  interface{} // 创建者
	UpdatedBy  interface{} // 更新者
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
	DeletedAt  *gtime.Time // 删除时间
}
