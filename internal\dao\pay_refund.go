// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// payRefundDao is the data access object for the table hg_pay_refund.
// You can define custom methods on it to extend its functionality as needed.
type payRefundDao struct {
	*internal.PayRefundDao
}

var (
	// PayRefund is a globally accessible object for table hg_pay_refund operations.
	PayRefund = payRefundDao{internal.NewPayRefundDao()}
)

// Add your custom methods and functionality below.
