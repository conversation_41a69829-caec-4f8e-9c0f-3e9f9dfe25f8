// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CameraInfoDao is the data access object for the table hg_camera_info.
type CameraInfoDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  CameraInfoColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// CameraInfoColumns defines and stores column names for the table hg_camera_info.
type CameraInfoColumns struct {
	Id         string //
	AreaId     string // 区域id
	CameraCode string // 摄像头编码
	CameraName string // 摄像头名称
	CameraType string // 摄像头类型:hik,dahua,uniview
	DdCode     string // 调度系统code
	HikCode    string // 海康系统内唯一物理id
	Location   string // 安装位置
	Direction  string // 朝向
	Status     string // 状态:1启用,2禁用
	Remark     string // 备注
	CreatedBy  string // 创建者
	UpdatedBy  string // 更新者
	CreatedAt  string //
	UpdatedAt  string //
	DeletedAt  string // 删除时间
}

// cameraInfoColumns holds the columns for the table hg_camera_info.
var cameraInfoColumns = CameraInfoColumns{
	Id:         "id",
	AreaId:     "area_id",
	CameraCode: "camera_code",
	CameraName: "camera_name",
	CameraType: "camera_type",
	DdCode:     "dd_code",
	HikCode:    "hik_code",
	Location:   "location",
	Direction:  "direction",
	Status:     "status",
	Remark:     "remark",
	CreatedBy:  "created_by",
	UpdatedBy:  "updated_by",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	DeletedAt:  "deleted_at",
}

// NewCameraInfoDao creates and returns a new DAO object for table data access.
func NewCameraInfoDao(handlers ...gdb.ModelHandler) *CameraInfoDao {
	return &CameraInfoDao{
		group:    "default",
		table:    "hg_camera_info",
		columns:  cameraInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CameraInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CameraInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CameraInfoDao) Columns() CameraInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CameraInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CameraInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CameraInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
