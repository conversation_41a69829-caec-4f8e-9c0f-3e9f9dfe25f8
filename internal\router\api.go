// Package router

package router

import (
	"context"
	"intellos/internal/consts"
	"intellos/internal/controller/api/hik"
	"intellos/internal/controller/api/member"
	"intellos/internal/controller/api/pay"
	"intellos/internal/controller/api/strategy"
	"intellos/internal/service"
	"intellos/utility/simple"

	"github.com/gogf/gf/v2/net/ghttp"
)

// Api 前台路由
func Api(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group(simple.RouterPrefix(ctx, consts.AppApi), func(group *ghttp.RouterGroup) {
		group.Bind(
			pay.NewV1(), // 支付异步通知
		)
		group.Bind(hik.NewV1())      // 海康回调列表
		group.Bind(strategy.NewV1()) // 策略相关接口
		group.Middleware(service.Middleware().ApiAuth)
		group.Bind(
			member.NewV1(), // 管理员
		)
	})
}
