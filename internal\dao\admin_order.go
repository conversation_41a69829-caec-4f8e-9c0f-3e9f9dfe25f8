// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// adminOrderDao is the data access object for the table hg_admin_order.
// You can define custom methods on it to extend its functionality as needed.
type adminOrderDao struct {
	*internal.AdminOrderDao
}

var (
	// AdminOrder is a globally accessible object for table hg_admin_order operations.
	AdminOrder = adminOrderDao{internal.NewAdminOrderDao()}
)

// Add your custom methods and functionality below.
