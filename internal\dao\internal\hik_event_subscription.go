// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HikEventSubscriptionDao is the data access object for the table hg_hik_event_subscription.
type HikEventSubscriptionDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  HikEventSubscriptionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// HikEventSubscriptionColumns defines and stores column names for the table hg_hik_event_subscription.
type HikEventSubscriptionColumns struct {
	Id              string // ID
	Ability         string // 事件类别
	EventId         string // 事件唯一标识
	EventType       string // 事件类型
	SrcIndex        string // 事件源编号
	SrcType         string // 事件源类型
	FaceUrl         string // 人脸图片URI
	FaceTime        string // 抓拍图片的时间
	CertificateType string // 目标对应的人脸的证件类型
	Certificate     string // 目标对应的人脸的证件号码
	FacePicUrl      string // 目标人脸的图片
	HappenTime      string // 事件发生时间
	SendTime        string // 发送时间
	FaceInfoSex     string // 性别
	FaceInfoName    string // 用户信息 (姓名_性别_)
	SrcName         string // 工作单位
	ResIndexCode    string // 资源的唯一标识
	ResCn           string // 资源的名称
	ResResourceType string // 资源类型
}

// hikEventSubscriptionColumns holds the columns for the table hg_hik_event_subscription.
var hikEventSubscriptionColumns = HikEventSubscriptionColumns{
	Id:              "id",
	Ability:         "ability",
	EventId:         "event_id",
	EventType:       "event_type",
	SrcIndex:        "src_index",
	SrcType:         "src_type",
	FaceUrl:         "faceUrl",
	FaceTime:        "faceTime",
	CertificateType: "certificateType",
	Certificate:     "certificate",
	FacePicUrl:      "facePicUrl",
	HappenTime:      "happen_time",
	SendTime:        "send_time",
	FaceInfoSex:     "face_info_sex",
	FaceInfoName:    "face_info_name",
	SrcName:         "src_name",
	ResIndexCode:    "res_index_code",
	ResCn:           "res_cn",
	ResResourceType: "res_resource_type",
}

// NewHikEventSubscriptionDao creates and returns a new DAO object for table data access.
func NewHikEventSubscriptionDao(handlers ...gdb.ModelHandler) *HikEventSubscriptionDao {
	return &HikEventSubscriptionDao{
		group:    "default",
		table:    "hg_hik_event_subscription",
		columns:  hikEventSubscriptionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HikEventSubscriptionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HikEventSubscriptionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HikEventSubscriptionDao) Columns() HikEventSubscriptionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HikEventSubscriptionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HikEventSubscriptionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HikEventSubscriptionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
