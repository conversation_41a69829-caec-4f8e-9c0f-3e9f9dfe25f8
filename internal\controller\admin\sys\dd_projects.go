// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/ddprojects"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	DdProjects = cDdProjects{}
)

type cDdProjects struct{}

// List 查看调度系统同步最新评委会接口列表
func (c *cDdProjects) List(ctx context.Context, req *ddprojects.ListReq) (res *ddprojects.ListRes, err error) {
	list, totalCount, err := service.SysDdProjects().List(ctx, &req.DdProjectsListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DdProjectsListModel{}
	}

	res = new(ddprojects.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出调度系统同步最新评委会接口列表
func (c *cDdProjects) Export(ctx context.Context, req *ddprojects.ExportReq) (res *ddprojects.ExportRes, err error) {
	err = service.SysDdProjects().Export(ctx, &req.DdProjectsListInp)
	return
}

// Edit 更新调度系统同步最新评委会接口
func (c *cDdProjects) Edit(ctx context.Context, req *ddprojects.EditReq) (res *ddprojects.EditRes, err error) {
	err = service.SysDdProjects().Edit(ctx, &req.DdProjectsEditInp)
	return
}

// View 获取指定调度系统同步最新评委会接口信息
func (c *cDdProjects) View(ctx context.Context, req *ddprojects.ViewReq) (res *ddprojects.ViewRes, err error) {
	data, err := service.SysDdProjects().View(ctx, &req.DdProjectsViewInp)
	if err != nil {
		return
	}

	res = new(ddprojects.ViewRes)
	res.DdProjectsViewModel = data
	return
}

// Delete 删除调度系统同步最新评委会接口
func (c *cDdProjects) Delete(ctx context.Context, req *ddprojects.DeleteReq) (res *ddprojects.DeleteRes, err error) {
	err = service.SysDdProjects().Delete(ctx, &req.DdProjectsDeleteInp)
	return
}