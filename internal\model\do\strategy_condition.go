// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyCondition is the golang structure of table hg_strategy_condition for DAO operations like Where/Data.
type StrategyCondition struct {
	g.Meta          `orm:"table:hg_strategy_condition, do:true"`
	Id              interface{} //
	StrategyId      interface{} // 策略主表ID
	ConditionCode   interface{} // 条件编码
	ConditionName   interface{} // 条件名称
	ConditionType   interface{} // 条件类型
	ConditionConfig *gjson.Json // 条件配置参数
	IsConfigurable  interface{} // 是否可配置:1可配置,0写死
	IsEnabled       interface{} // 是否启用
	SortOrder       interface{} // 排序
	CreatedAt       *gtime.Time //
	UpdatedAt       *gtime.Time //
}
