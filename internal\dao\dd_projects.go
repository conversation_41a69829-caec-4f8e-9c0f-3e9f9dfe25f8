// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// ddProjectsDao is the data access object for the table hg_dd_projects.
// You can define custom methods on it to extend its functionality as needed.
type ddProjectsDao struct {
	*internal.DdProjectsDao
}

var (
	// DdProjects is a globally accessible object for table hg_dd_projects operations.
	DdProjects = ddProjectsDao{internal.NewDdProjectsDao()}
)

// Add your custom methods and functionality below.
