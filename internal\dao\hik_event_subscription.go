// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// hikEventSubscriptionDao is the data access object for the table hg_hik_event_subscription.
// You can define custom methods on it to extend its functionality as needed.
type hikEventSubscriptionDao struct {
	*internal.HikEventSubscriptionDao
}

var (
	// HikEventSubscription is a globally accessible object for table hg_hik_event_subscription operations.
	HikEventSubscription = hikEventSubscriptionDao{internal.NewHikEventSubscriptionDao()}
)

// Add your custom methods and functionality below.
