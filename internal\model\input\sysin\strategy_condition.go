// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// StrategyConditionUpdateFields 修改策略条件字段过滤
type StrategyConditionUpdateFields struct {
	StrategyId      int64       `json:"strategyId"      dc:"策略主表ID"`
	ConditionCode   string      `json:"conditionCode"   dc:"条件编码"`
	ConditionName   string      `json:"conditionName"   dc:"条件名称"`
	ConditionType   string      `json:"conditionType"   dc:"条件类型"`
	ConditionConfig *gjson.Json `json:"conditionConfig" dc:"条件配置参数"`
	IsConfigurable  int         `json:"isConfigurable"  dc:"是否可配置:1可配置,0写死"`
	IsEnabled       int         `json:"isEnabled"       dc:"是否启用"`
	SortOrder       int         `json:"sortOrder"       dc:"排序"`
}

// StrategyConditionInsertFields 新增策略条件字段过滤
type StrategyConditionInsertFields struct {
	StrategyId      int64       `json:"strategyId"      dc:"策略主表ID"`
	ConditionCode   string      `json:"conditionCode"   dc:"条件编码"`
	ConditionName   string      `json:"conditionName"   dc:"条件名称"`
	ConditionType   string      `json:"conditionType"   dc:"条件类型"`
	ConditionConfig *gjson.Json `json:"conditionConfig" dc:"条件配置参数"`
	IsConfigurable  int         `json:"isConfigurable"  dc:"是否可配置:1可配置,0写死"`
	IsEnabled       int         `json:"isEnabled"       dc:"是否启用"`
	SortOrder       int         `json:"sortOrder"       dc:"排序"`
}

// StrategyConditionEditInp 修改/新增策略条件
type StrategyConditionEditInp struct {
	entity.StrategyCondition
}

func (in *StrategyConditionEditInp) Filter(ctx context.Context) (err error) {
	// 验证策略主表ID
	if err := g.Validator().Rules("required").Data(in.StrategyId).Messages("策略主表ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证条件编码
	if err := g.Validator().Rules("required").Data(in.ConditionCode).Messages("条件编码不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证条件名称
	if err := g.Validator().Rules("required").Data(in.ConditionName).Messages("条件名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证条件类型
	if err := g.Validator().Rules("required").Data(in.ConditionType).Messages("条件类型不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证条件配置参数
	if err := g.Validator().Rules("required").Data(in.ConditionConfig).Messages("条件配置参数不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type StrategyConditionEditModel struct{}

// StrategyConditionDeleteInp 删除策略条件
type StrategyConditionDeleteInp struct {
	Id interface{} `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyConditionDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyConditionDeleteModel struct{}

// StrategyConditionViewInp 获取指定策略条件信息
type StrategyConditionViewInp struct {
	Id int64 `json:"id" v:"required#id不能为空" dc:"id"`
}

func (in *StrategyConditionViewInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyConditionViewModel struct {
	entity.StrategyCondition
}

// StrategyConditionListInp 获取策略条件列表
type StrategyConditionListInp struct {
	form.PageReq
	Id            int64         `json:"id"            dc:"id"`
	ConditionCode string        `json:"conditionCode" dc:"条件编码"`
	ConditionName string        `json:"conditionName" dc:"条件名称"`
	CreatedAt     []*gtime.Time `json:"createdAt"     dc:"created_at"`
}

func (in *StrategyConditionListInp) Filter(ctx context.Context) (err error) {
	return
}

type StrategyConditionListModel struct {
	Id              int64       `json:"id"              dc:"id"`
	StrategyId      int64       `json:"strategyId"      dc:"策略主表ID"`
	ConditionCode   string      `json:"conditionCode"   dc:"条件编码"`
	ConditionName   string      `json:"conditionName"   dc:"条件名称"`
	ConditionType   string      `json:"conditionType"   dc:"条件类型"`
	ConditionConfig *gjson.Json `json:"conditionConfig" dc:"条件配置参数"`
	IsConfigurable  int         `json:"isConfigurable"  dc:"是否可配置:1可配置,0写死"`
	IsEnabled       int         `json:"isEnabled"       dc:"是否启用"`
	SortOrder       int         `json:"sortOrder"       dc:"排序"`
	CreatedAt       *gtime.Time `json:"createdAt"       dc:"created_at"`
	UpdatedAt       *gtime.Time `json:"updatedAt"       dc:"updated_at"`
}

// StrategyConditionExportModel 导出策略条件
type StrategyConditionExportModel struct {
	Id             int64       `json:"id"             dc:"id"`
	StrategyId     int64       `json:"strategyId"     dc:"策略主表ID"`
	ConditionCode  string      `json:"conditionCode"  dc:"条件编码"`
	ConditionName  string      `json:"conditionName"  dc:"条件名称"`
	ConditionType  string      `json:"conditionType"  dc:"条件类型"`
	IsConfigurable int         `json:"isConfigurable" dc:"是否可配置:1可配置,0写死"`
	IsEnabled      int         `json:"isEnabled"      dc:"是否启用"`
	SortOrder      int         `json:"sortOrder"      dc:"排序"`
	CreatedAt      *gtime.Time `json:"createdAt"      dc:"created_at"`
	UpdatedAt      *gtime.Time `json:"updatedAt"      dc:"updated_at"`
}
