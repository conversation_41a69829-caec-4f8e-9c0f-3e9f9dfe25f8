// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"intellos/internal/dao/internal"
)

// strategyDao is the data access object for the table hg_strategy.
// You can define custom methods on it to extend its functionality as needed.
type strategyDao struct {
	*internal.StrategyDao
}

var (
	// Strategy is a globally accessible object for table hg_strategy operations.
	Strategy = strategyDao{internal.NewStrategyDao()}
)

// Add your custom methods and functionality below.
