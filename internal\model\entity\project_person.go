// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ProjectPerson is the golang structure for table project_person.
type ProjectPerson struct {
	Id                 int64       `json:"id"                 orm:"id"                 description:"ID"`
	ProjectId          int64       `json:"projectId"          orm:"project_id"         description:""`
	BidCouncilId       string      `json:"bidCouncilId"       orm:"bid_council_id"     description:""`
	IdCard             string      `json:"idCard"             orm:"id_card"            description:"身份证号"`
	Name               string         `json:"name"               orm:"name"               description:"姓名"`
	Phone              string      `json:"phone"              orm:"phone"              description:"手机号"`
	RoleId             int         `json:"roleId"             orm:"role_id"            description:"角色id"`
	VerifyCode         string      `json:"verifyCode"         orm:"verify_code"        description:""`
	WorkOrg            string      `json:"workOrg"            orm:"work_org"           description:"工作单位"`
	ReportTime         string         `json:"reportTime"         orm:"report_time"        description:""`
	SignoutAt          string         `json:"signoutAt"          orm:"signout_at"         description:""`
	Status             int         `json:"status"             orm:"status"             description:"1-已同步，2-在评标室，3-离开"`
	RoomId             int         `json:"roomId"             orm:"room_id"            description:"房间id"`
	Source             string      `json:"source"             orm:"source"             description:""`
	CommitmentPdfStamp string      `json:"commitmentPdfStamp" orm:"commitmentPdfStamp" description:""`
	CreatedBy          int64       `json:"createdBy"          orm:"created_by"         description:"创建者"`
	UpdatedBy          int64       `json:"updatedBy"          orm:"updated_by"         description:"更新者"`
	CreatedAt          *gtime.Time `json:"createdAt"          orm:"created_at"         description:"创建时间"`
	UpdatedAt          *gtime.Time `json:"updatedAt"          orm:"updated_at"         description:"修改时间"`
	DeletedAt          *gtime.Time `json:"deletedAt"          orm:"deleted_at"         description:"删除时间"`
}
