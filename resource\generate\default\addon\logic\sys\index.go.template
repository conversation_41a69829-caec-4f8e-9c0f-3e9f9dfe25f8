// Package sys

package sys

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	"intellos/addons/@{.name}/global"
	"intellos/addons/@{.name}/model/input/sysin"
	"intellos/addons/@{.name}/service"
	"intellos/internal/library/contexts"
)

type sSysIndex struct{}

func NewSysIndex() *sSysIndex {
	return &sSysIndex{}
}

func init() {
	service.RegisterSysIndex(NewSysIndex())
}

// Test 测试
func (s *sSysIndex) Test(ctx context.Context, in *sysin.IndexTestInp) (res *sysin.IndexTestModel, err error) {
	res = new(sysin.IndexTestModel)
	res.Name = in.Name
	res.Module = fmt.Sprintf("当前插件模块是：%s，当前应用模块是：%s", global.GetSkeleton().Name, contexts.Get(ctx).Module)
	res.Time = gtime.Now()
	return
}
