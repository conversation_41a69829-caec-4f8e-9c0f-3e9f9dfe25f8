// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"intellos/api/admin/projectperson"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

var (
	ProjectPerson = cProjectPerson{}
)

type cProjectPerson struct{}

// List 查看调度系统项目人员列表列表
func (c *cProjectPerson) List(ctx context.Context, req *projectperson.ListReq) (res *projectperson.ListRes, err error) {
	list, totalCount, err := service.SysProjectPerson().List(ctx, &req.ProjectPersonListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.ProjectPersonListModel{}
	}

	res = new(projectperson.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出调度系统项目人员列表列表
func (c *cProjectPerson) Export(ctx context.Context, req *projectperson.ExportReq) (res *projectperson.ExportRes, err error) {
	err = service.SysProjectPerson().Export(ctx, &req.ProjectPersonListInp)
	return
}

// Edit 更新调度系统项目人员列表
func (c *cProjectPerson) Edit(ctx context.Context, req *projectperson.EditReq) (res *projectperson.EditRes, err error) {
	err = service.SysProjectPerson().Edit(ctx, &req.ProjectPersonEditInp)
	return
}

// View 获取指定调度系统项目人员列表信息
func (c *cProjectPerson) View(ctx context.Context, req *projectperson.ViewReq) (res *projectperson.ViewRes, err error) {
	data, err := service.SysProjectPerson().View(ctx, &req.ProjectPersonViewInp)
	if err != nil {
		return
	}

	res = new(projectperson.ViewRes)
	res.ProjectPersonViewModel = data
	return
}

// Delete 删除调度系统项目人员列表
func (c *cProjectPerson) Delete(ctx context.Context, req *projectperson.DeleteReq) (res *projectperson.DeleteRes, err error) {
	err = service.SysProjectPerson().Delete(ctx, &req.ProjectPersonDeleteInp)
	return
}

// Status 更新调度系统项目人员列表状态
func (c *cProjectPerson) Status(ctx context.Context, req *projectperson.StatusReq) (res *projectperson.StatusRes, err error) {
	err = service.SysProjectPerson().Status(ctx, &req.ProjectPersonStatusInp)
	return
}